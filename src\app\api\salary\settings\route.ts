import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requireAuth } from '@/lib/auth'

// GET - Get current salary settings
export async function GET() {
  try {
    const user = await requireAuth()
    const supabase = await createClient()

    // Check permissions - only system admins can view salary settings
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'system_admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بعرض إعدادات الراتب' },
        { status: 403 }
      )
    }

    // Get current active salary settings
    const { data: settings, error } = await supabase
      .from('salary_settings')
      .select(`
        *,
        created_by_user:profiles!salary_settings_created_by_fkey(
          id, full_name, email
        )
      `)
      .eq('is_active', true)
      .order('effective_from', { ascending: false })
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching salary settings:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب إعدادات الراتب' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      settings: settings || null
    })

  } catch (error: unknown) {
    console.error('Error in salary settings API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - Create new salary settings
export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    const supabase = await createClient()

    const body = await request.json()
    const { 
      base_salary, 
      working_hours_per_day, 
      currency = 'SAR', 
      effective_from 
    } = body

    // Validate input
    if (!base_salary || !working_hours_per_day || !effective_from) {
      return NextResponse.json(
        { error: 'الراتب الأساسي وساعات العمل اليومية وتاريخ السريان مطلوبة' },
        { status: 400 }
      )
    }

    if (base_salary <= 0 || working_hours_per_day <= 0) {
      return NextResponse.json(
        { error: 'الراتب الأساسي وساعات العمل يجب أن تكون أكبر من صفر' },
        { status: 400 }
      )
    }

    // Check permissions - only system admins can create salary settings
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'system_admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بإنشاء إعدادات الراتب' },
        { status: 403 }
      )
    }

    // Deactivate current active settings
    await supabase
      .from('salary_settings')
      .update({ is_active: false })
      .eq('is_active', true)

    // Create new salary settings
    const settingsData = {
      base_salary: parseFloat(base_salary),
      working_hours_per_day: parseFloat(working_hours_per_day),
      currency,
      effective_from,
      is_active: true,
      created_by: user.id
    }

    const { data: settings, error: createError } = await supabase
      .from('salary_settings')
      .insert(settingsData)
      .select(`
        *,
        created_by_user:profiles!salary_settings_created_by_fkey(
          id, full_name, email
        )
      `)
      .single()

    if (createError) {
      console.error('Error creating salary settings:', createError)
      return NextResponse.json(
        { error: 'حدث خطأ في إنشاء إعدادات الراتب' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      settings
    })

  } catch (error: unknown) {
    console.error('Error in salary settings creation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - Update salary settings
export async function PUT(request: NextRequest) {
  try {
    const user = await requireAuth()
    const supabase = await createClient()

    const body = await request.json()
    const { 
      settings_id,
      base_salary, 
      working_hours_per_day, 
      currency,
      effective_from,
      is_active
    } = body

    // Validate input
    if (!settings_id) {
      return NextResponse.json(
        { error: 'معرف الإعدادات مطلوب' },
        { status: 400 }
      )
    }

    // Check permissions - only system admins can update salary settings
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'system_admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بتحديث إعدادات الراتب' },
        { status: 403 }
      )
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (base_salary !== undefined) {
      if (base_salary <= 0) {
        return NextResponse.json(
          { error: 'الراتب الأساسي يجب أن يكون أكبر من صفر' },
          { status: 400 }
        )
      }
      updateData.base_salary = parseFloat(base_salary)
    }

    if (working_hours_per_day !== undefined) {
      if (working_hours_per_day <= 0) {
        return NextResponse.json(
          { error: 'ساعات العمل اليومية يجب أن تكون أكبر من صفر' },
          { status: 400 }
        )
      }
      updateData.working_hours_per_day = parseFloat(working_hours_per_day)
    }

    if (currency !== undefined) {
      updateData.currency = currency
    }

    if (effective_from !== undefined) {
      updateData.effective_from = effective_from
    }

    if (is_active !== undefined) {
      updateData.is_active = is_active
      
      // If activating this setting, deactivate others
      if (is_active) {
        await supabase
          .from('salary_settings')
          .update({ is_active: false })
          .eq('is_active', true)
          .neq('id', settings_id)
      }
    }

    // Update salary settings
    const { data: settings, error: updateError } = await supabase
      .from('salary_settings')
      .update(updateData)
      .eq('id', settings_id)
      .select(`
        *,
        created_by_user:profiles!salary_settings_created_by_fkey(
          id, full_name, email
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating salary settings:', updateError)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث إعدادات الراتب' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      settings
    })

  } catch (error: unknown) {
    console.error('Error in salary settings update API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
