'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, TrendingUp, Users, DollarSign, Download, Filter, Search, FileSpreadsheet } from 'lucide-react'
import { EnhancedSalesDataTable } from './EnhancedSalesDataTable'
import { SalesMetricsCards } from './SalesMetricsCards'
import { SalesCharts } from './SalesCharts'
import { SimpleDatePicker } from '@/components/ui/simple-date-picker'
import { EmployeeSearch } from '@/components/ui/employee-search'
import { createClient } from '@/lib/supabase/client'
import { useToast } from '@/hooks/use-toast'
import { exportToCSV, exportToExcel, exportSalesData } from '@/lib/export-utils'
import type { Database } from '@/lib/supabase'

type Profile = Database['public']['Tables']['profiles']['Row']
type DailyClosing = Database['public']['Tables']['daily_closings']['Row']

interface SalesData {
  id: string
  closing_date: string
  user_id: string
  total_sales_amount: number
  cash_delivered: number
  deficit_amount: number
  advances_amount: number
  price_breaks_amount: number
  working_hours?: number
  employee: {
    id: string
    full_name: string
    email: string
    role: string
    area_id?: string
    team_id?: string
  }
  area?: {
    id: string
    name: string
  }
  team?: {
    id: string
    name: string
  }
}

interface SalesTrackingDashboardProps {
  userRole: string
  userId: string
  userAreaId?: string
  userTeamId?: string
}

export function SalesTrackingDashboard({ 
  userRole, 
  userId, 
  userAreaId, 
  userTeamId 
}: SalesTrackingDashboardProps) {
  const [salesData, setSalesData] = useState<SalesData[]>([])
  const [filteredData, setFilteredData] = useState<SalesData[]>([])
  const [loading, setLoading] = useState(true)
  const [employees, setEmployees] = useState<Profile[]>([])
  const [areas, setAreas] = useState<any[]>([])
  const [teams, setTeams] = useState<any[]>([])
  
  // Filters
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // Start of current month
    to: new Date() // Today
  })
  const [selectedEmployee, setSelectedEmployee] = useState<string>('all')
  const [selectedArea, setSelectedArea] = useState<string>('all')
  const [selectedTeam, setSelectedTeam] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')

  const supabase = createClient()
  const { toast } = useToast()

  useEffect(() => {
    fetchInitialData()
  }, [])

  useEffect(() => {
    fetchSalesData()
  }, [dateRange, selectedEmployee, selectedArea, selectedTeam])

  useEffect(() => {
    applyFilters()
  }, [salesData, searchQuery])

  const fetchInitialData = async () => {
    try {
      console.log('Fetching initial data via API...')

      const response = await fetch('/api/initial-data')
      const result = await response.json()

      console.log('Initial data API response:', result)

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`)
      }

      if (!result.success) {
        throw new Error(result.error || 'API request failed')
      }

      const { employees, areas, teams } = result.data

      console.log('Setting initial data:', {
        employeesCount: employees.length,
        areasCount: areas.length,
        teamsCount: teams.length
      })

      setEmployees(employees)
      setAreas(areas)
      setTeams(teams)

    } catch (error) {
      console.error('Error fetching initial data:', error)
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      toast.error(`حدث خطأ في تحميل البيانات الأولية: ${errorMessage}`)
    }
  }

  const fetchSalesData = async () => {
    try {
      setLoading(true)

      console.log('Starting fetchSalesData via API...')
      console.log('User role:', userRole)
      console.log('User ID:', userId)
      console.log('Date range:', dateRange)
      console.log('Selected employee:', selectedEmployee)
      console.log('Selected area:', selectedArea)
      console.log('Selected team:', selectedTeam)

      // Build query parameters
      const params = new URLSearchParams()

      if (dateRange.from) {
        params.append('dateFrom', dateRange.from.toISOString().split('T')[0])
      }
      if (dateRange.to) {
        params.append('dateTo', dateRange.to.toISOString().split('T')[0])
      }
      if (selectedEmployee && selectedEmployee !== 'all') {
        params.append('selectedEmployee', selectedEmployee)
      }
      if (selectedArea && selectedArea !== 'all') {
        params.append('selectedArea', selectedArea)
      }
      if (selectedTeam && selectedTeam !== 'all') {
        params.append('selectedTeam', selectedTeam)
      }

      const url = `/api/sales-data?${params.toString()}`
      console.log('Fetching from URL:', url)

      const response = await fetch(url)
      const result = await response.json()

      console.log('API response:', result)

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`)
      }

      if (!result.success) {
        throw new Error(result.error || 'API request failed')
      }

      console.log('Setting sales data:', result.data.length, 'records')
      setSalesData(result.data)
    } catch (error) {
      console.error('Error fetching sales data:', error)
      console.error('Error type:', typeof error)
      console.error('Error constructor:', error?.constructor?.name)
      console.error('Error keys:', Object.keys(error || {}))

      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      toast.error(`حدث خطأ في تحميل بيانات المبيعات: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...salesData]

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(item =>
        item.employee.full_name.toLowerCase().includes(query) ||
        item.employee.email.toLowerCase().includes(query) ||
        item.area?.name.toLowerCase().includes(query) ||
        item.team?.name.toLowerCase().includes(query)
      )
    }

    // Apply area filter
    if (selectedArea && selectedArea !== 'all') {
      filtered = filtered.filter(item => item.employee.area_id === selectedArea)
    }

    // Apply team filter
    if (selectedTeam && selectedTeam !== 'all') {
      filtered = filtered.filter(item => item.employee.team_id === selectedTeam)
    }

    setFilteredData(filtered)
  }

  const handleExportCSV = () => {
    // Transform data for sales export
    const salesData = filteredData.map(item => ({
      date: item.closing_date,
      employee: item.employee,
      sales_amount: item.total_sales_amount || 0,
      working_hours: item.working_hours || 0,
      deficit_amount: item.deficit_amount || 0,
      efficiency: item.working_hours ? (item.working_hours / 8) * 100 : 0,
      performance_rating: item.working_hours >= 8 ? 'excellent' :
                         item.working_hours >= 6 ? 'good' :
                         item.working_hours >= 4 ? 'average' : 'poor'
    }))

    const { columns } = exportSalesData(salesData)
    exportToCSV(salesData, columns, {
      filename: 'sales_tracking_data',
      includeTimestamp: true,
      rtlSupport: true
    })
  }

  const handleExportExcel = () => {
    // Transform data for sales export
    const salesData = filteredData.map(item => ({
      date: item.closing_date,
      employee: item.employee,
      sales_amount: item.total_sales_amount || 0,
      working_hours: item.working_hours || 0,
      deficit_amount: item.deficit_amount || 0,
      efficiency: item.working_hours ? (item.working_hours / 8) * 100 : 0,
      performance_rating: item.working_hours >= 8 ? 'excellent' :
                         item.working_hours >= 6 ? 'good' :
                         item.working_hours >= 4 ? 'average' : 'poor'
    }))

    const { columns } = exportSalesData(salesData)
    exportToExcel(salesData, columns, {
      filename: 'sales_tracking_data',
      includeTimestamp: true,
      rtlSupport: true
    })
  }

  return (
    <div className="rtl-container space-y-6" dir="rtl" style={{ maxWidth: '100vw', overflowX: 'hidden' }}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">تتبع المبيعات</h2>
          <p className="text-muted-foreground">
            عرض وتحليل بيانات المبيعات اليومية والأداء
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleExportCSV} variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            تصدير CSV
          </Button>
          <Button onClick={handleExportExcel} variant="outline" className="gap-2">
            <FileSpreadsheet className="h-4 w-4" />
            تصدير Excel
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            الفلاتر والبحث
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Date Range Filter */}
            <div className="space-y-2">
              <Label>نطاق التاريخ</Label>
              <SimpleDatePicker
                date={dateRange}
                onDateChange={setDateRange}
              />
            </div>

            {/* Employee Filter */}
            {userRole !== 'sales_employee' && (
              <div className="space-y-2">
                <Label>موظف المبيعات</Label>
                <EmployeeSearch
                  employees={employees}
                  selectedEmployee={selectedEmployee}
                  onEmployeeChange={setSelectedEmployee}
                />
              </div>
            )}

            {/* Area Filter */}
            {userRole === 'system_admin' && (
              <div className="space-y-2">
                <Label>المنطقة</Label>
                <Select value={selectedArea} onValueChange={setSelectedArea}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع المناطق" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع المناطق</SelectItem>
                    {areas.map(area => (
                      <SelectItem key={area.id} value={area.id}>
                        {area.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Team Filter */}
            {(userRole === 'system_admin' || userRole === 'area_manager') && (
              <div className="space-y-2">
                <Label>الفريق</Label>
                <Select value={selectedTeam} onValueChange={setSelectedTeam}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الفرق" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفرق</SelectItem>
                    {teams
                      .filter(team => !selectedArea || selectedArea === 'all' || team.area_id === selectedArea)
                      .map(team => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Search */}
          <div className="space-y-2">
            <Label>البحث</Label>
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="البحث في أسماء الموظفين، البريد الإلكتروني، المناطق، أو الفرق..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Metrics Cards */}
      <SalesMetricsCards data={filteredData} loading={loading} />

      {/* Main Content Tabs */}
      <Tabs defaultValue="table" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="table" className="gap-2">
            <Users className="h-4 w-4" />
            جدول البيانات
          </TabsTrigger>
          <TabsTrigger value="charts" className="gap-2">
            <TrendingUp className="h-4 w-4" />
            الرسوم البيانية
          </TabsTrigger>
          <TabsTrigger value="analytics" className="gap-2">
            <DollarSign className="h-4 w-4" />
            التحليلات
          </TabsTrigger>
        </TabsList>

        <TabsContent value="table">
          <EnhancedSalesDataTable data={filteredData} loading={loading} />
        </TabsContent>

        <TabsContent value="charts">
          <SalesCharts data={filteredData} loading={loading} />
        </TabsContent>

        <TabsContent value="analytics">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>التحليلات المتقدمة</CardTitle>
                <CardDescription>
                  تحليل مفصل لأداء المبيعات والاتجاهات
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  سيتم إضافة التحليلات المتقدمة قريباً...
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
