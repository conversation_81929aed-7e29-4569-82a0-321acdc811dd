'use client'

import * as React from 'react'
import { CalendarIcon, X } from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import { DateRange } from 'react-day-picker'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

interface DateRangePickerProps {
  date: {
    from: Date | undefined
    to: Date | undefined
  }
  onDateChange: (date: { from: Date | undefined; to: Date | undefined }) => void
  className?: string
  placeholder?: string
}

export function DateRangePicker({
  date,
  onDateChange,
  className,
  placeholder = 'اختر نطاق التاريخ'
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)

  const handleSelect = (range: DateRange | undefined) => {
    onDateChange({
      from: range?.from,
      to: range?.to
    })
    // Close popover when both dates are selected
    if (range?.from && range?.to) {
      setIsOpen(false)
    }
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDateChange({ from: undefined, to: undefined })
  }

  return (
    <div className={cn('grid gap-2', className)} dir="rtl">
      <div className="relative">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              id="date"
              variant={'outline'}
              className={cn(
                'w-full justify-between text-right font-normal h-10 px-3 py-2 pr-10',
                !date.from && 'text-muted-foreground'
              )}
            >
              <CalendarIcon className="h-4 w-4" />
              <span className="flex-1 text-right">
                {date?.from ? (
                  date.to ? (
                    <>
                      {format(date.from, 'dd/MM/yyyy', { locale: ar })} - {' '}
                      {format(date.to, 'dd/MM/yyyy', { locale: ar })}
                    </>
                  ) : (
                    format(date.from, 'dd/MM/yyyy', { locale: ar })
                  )
                ) : (
                  <span>{placeholder}</span>
                )}
              </span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start" dir="rtl">
            <div className="p-3 border-b">
              <h4 className="font-medium text-sm text-right">اختر نطاق التاريخ</h4>
            </div>
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from || new Date()}
              selected={{
                from: date.from,
                to: date.to
              }}
              onSelect={handleSelect}
              numberOfMonths={1}
              locale={ar}
              dir="rtl"
              className="rtl"
            />
            <div className="p-3 border-t flex justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                إلغاء
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
              >
                مسح
              </Button>
            </div>
          </PopoverContent>
        </Popover>
        {(date?.from || date?.to) && (
          <button
            type="button"
            className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            onClick={handleClear}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">مسح</span>
          </button>
        )}
      </div>
    </div>
  )
}
