'use client'

import * as React from 'react'
import { CalendarIcon, X, Check } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

interface MonthPickerProps {
  months: number[]
  year: number | undefined
  onMonthYearChange: (months: number[], year: number | undefined) => void
  className?: string
  placeholder?: string
}

const getMonthName = (month: number): string => {
  const months = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ]
  return months[month - 1] || ''
}

export function MonthPicker({
  months,
  year,
  onMonthYearChange,
  className,
  placeholder = 'اختر الشهور والسنة'
}: MonthPickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [tempMonths, setTempMonths] = React.useState<number[]>(months)
  const [tempYear, setTempYear] = React.useState<number | undefined>(year)

  const currentYear = new Date().getFullYear()
  const availableYears = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i)

  const handleApply = () => {
    onMonthYearChange(tempMonths, tempYear)
    setIsOpen(false)
  }

  const handleClear = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation()
    }
    setTempMonths([])
    setTempYear(undefined)
    onMonthYearChange([], undefined)
    setIsOpen(false)
  }

  const handleCancel = () => {
    setTempMonths(months)
    setTempYear(year)
    setIsOpen(false)
  }

  const handleMonthToggle = (monthNum: number) => {
    setTempMonths(prev =>
      prev.includes(monthNum)
        ? prev.filter(m => m !== monthNum)
        : [...prev, monthNum].sort((a, b) => a - b)
    )
  }

  React.useEffect(() => {
    setTempMonths(months)
    setTempYear(year)
  }, [months, year])

  const displayText = months.length > 0 && year
    ? months.length === 1
      ? `${getMonthName(months[0])} ${year}`
      : `${months.length} شهور - ${year}`
    : placeholder

  return (
    <div className={cn('grid gap-2', className)} dir="rtl">
      <div className="relative">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              id="month-year"
              variant={'outline'}
              className={cn(
                'w-full justify-between text-right font-normal h-10 px-3 py-2 pr-10',
                months.length === 0 && !year && 'text-muted-foreground'
              )}
            >
              <CalendarIcon className="h-4 w-4" />
              <span className="flex-1 text-right">
                {displayText}
              </span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="end" dir="rtl">
            <div className="p-3 border-b">
              <h4 className="font-medium text-sm text-right">اختر الشهور والسنة</h4>
            </div>
            <div className="p-4 space-y-4">
              <div className="space-y-2">
                <Label>الشهور</Label>
                <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                  {Array.from({ length: 12 }, (_, i) => i + 1).map(monthNum => (
                    <div key={monthNum} className="flex items-center space-x-2 space-x-reverse">
                      <Checkbox
                        id={`month-${monthNum}`}
                        checked={tempMonths.includes(monthNum)}
                        onCheckedChange={() => handleMonthToggle(monthNum)}
                      />
                      <Label
                        htmlFor={`month-${monthNum}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {getMonthName(monthNum)}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>السنة</Label>
                <Select
                  value={tempYear?.toString() || ''}
                  onValueChange={(value) => setTempYear(value ? parseInt(value) : undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر السنة" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableYears.map(yearNum => (
                      <SelectItem key={yearNum} value={yearNum.toString()}>
                        {yearNum}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="p-3 border-t flex justify-between">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                >
                  إلغاء
                </Button>
                <Button
                  size="sm"
                  onClick={handleApply}
                  disabled={tempMonths.length === 0 || !tempYear}
                >
                  تطبيق
                </Button>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleClear()}
              >
                مسح
              </Button>
            </div>
          </PopoverContent>
        </Popover>
        {(months.length > 0 || year) && (
          <button
            type="button"
            className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground cursor-pointer"
            onClick={handleClear}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">مسح</span>
          </button>
        )}
      </div>
    </div>
  )
}
