import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requireAuth } from '@/lib/auth'

// GET - List employee advances
export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()
    const supabase = await createClient()

    const searchParams = request.nextUrl.searchParams
    const employee_id = searchParams.get('employee_id')
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50)
    const offset = (page - 1) * limit

    // Check permissions
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!userProfile) {
      return NextResponse.json(
        { error: 'ملف المستخدم غير موجود' },
        { status: 404 }
      )
    }

    let query = supabase
      .from('employee_advances')
      .select(`
        *,
        employee:profiles!employee_advances_employee_id_fkey(
          id, full_name, email, role
        ),
        approved_by_user:profiles!employee_advances_approved_by_fkey(
          id, full_name, email
        )
      `, { count: 'exact' })

    // Apply role-based filtering
    if (userProfile.role === 'sales_employee') {
      // Sales employees can only see their own advances
      query = query.eq('employee_id', user.id)
    } else if (userProfile.role !== 'system_admin') {
      // Non-admin users can only see advances for employees they manage
      // This would need additional logic based on team/area relationships
      // For now, restrict to system admins only for advance management
      return NextResponse.json(
        { error: 'غير مصرح لك بعرض السلف' },
        { status: 403 }
      )
    }

    // Apply filters
    if (employee_id) {
      query = query.eq('employee_id', employee_id)
    }

    if (status) {
      query = query.eq('status', status)
    }

    // Apply pagination and ordering
    const { data: advances, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching advances:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب بيانات السلف' },
        { status: 500 }
      )
    }

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      advances: advances || [],
      total: count || 0,
      page,
      limit,
      totalPages
    })

  } catch (error: unknown) {
    console.error('Error in advances list API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - Create new employee advance
export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    const supabase = await createClient()

    const body = await request.json()
    const { employee_id, amount, reason, deduct_from_month } = body

    // Validate input
    if (!employee_id || !amount || !reason) {
      return NextResponse.json(
        { error: 'معرف الموظف والمبلغ والسبب مطلوبة' },
        { status: 400 }
      )
    }

    if (amount <= 0) {
      return NextResponse.json(
        { error: 'المبلغ يجب أن يكون أكبر من صفر' },
        { status: 400 }
      )
    }

    // Check permissions - only system admins can create advances
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'system_admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بإنشاء سلفة' },
        { status: 403 }
      )
    }

    // Verify employee exists
    const { data: employee, error: employeeError } = await supabase
      .from('profiles')
      .select('id, full_name')
      .eq('id', employee_id)
      .single()

    if (employeeError || !employee) {
      return NextResponse.json(
        { error: 'الموظف غير موجود' },
        { status: 404 }
      )
    }

    // Create advance record
    const advanceData = {
      employee_id,
      amount,
      reason,
      deduct_from_month: deduct_from_month || null,
      status: 'pending',
      approved_by: user.id,
      approved_at: new Date().toISOString()
    }

    const { data: advance, error: createError } = await supabase
      .from('employee_advances')
      .insert(advanceData)
      .select(`
        *,
        employee:profiles!employee_advances_employee_id_fkey(
          id, full_name, email, role
        ),
        approved_by_user:profiles!employee_advances_approved_by_fkey(
          id, full_name, email
        )
      `)
      .single()

    if (createError) {
      console.error('Error creating advance:', createError)
      return NextResponse.json(
        { error: 'حدث خطأ في إنشاء السلفة' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      advance
    })

  } catch (error: unknown) {
    console.error('Error in advance creation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - Update advance status
export async function PUT(request: NextRequest) {
  try {
    const user = await requireAuth()
    const supabase = await createClient()

    const body = await request.json()
    const { advance_id, status, deduct_from_month } = body

    // Validate input
    if (!advance_id || !status) {
      return NextResponse.json(
        { error: 'معرف السلفة والحالة مطلوبة' },
        { status: 400 }
      )
    }

    if (!['pending', 'approved', 'deducted', 'cancelled'].includes(status)) {
      return NextResponse.json(
        { error: 'حالة السلفة غير صحيحة' },
        { status: 400 }
      )
    }

    // Check permissions - only system admins can update advances
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'system_admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بتحديث السلفة' },
        { status: 403 }
      )
    }

    // Update advance
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    }

    if (status === 'deducted' && deduct_from_month) {
      updateData.deducted_from_month = deduct_from_month
      updateData.deducted_at = new Date().toISOString()
    }

    const { data: advance, error: updateError } = await supabase
      .from('employee_advances')
      .update(updateData)
      .eq('id', advance_id)
      .select(`
        *,
        employee:profiles!employee_advances_employee_id_fkey(
          id, full_name, email, role
        ),
        approved_by_user:profiles!employee_advances_approved_by_fkey(
          id, full_name, email
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating advance:', updateError)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث السلفة' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      advance
    })

  } catch (error: unknown) {
    console.error('Error in advance update API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
