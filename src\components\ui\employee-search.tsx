'use client'

import * as React from 'react'
import { Check, ChevronsUpDown, Search, User, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'

interface Employee {
  id: string
  full_name: string
  email: string
  role: string
}

interface EmployeeSearchProps {
  employees: Employee[]
  selectedEmployee: string
  onEmployeeChange: (employeeId: string) => void
  placeholder?: string
  className?: string
}

export function EmployeeSearch({
  employees,
  selectedEmployee,
  onEmployeeChange,
  placeholder = 'البحث عن موظف...',
  className
}: EmployeeSearchProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')

  // Filter employees to show only sales employees
  const salesEmployees = employees.filter(emp => emp.role === 'sales_employee')

  const selectedEmployeeData = salesEmployees.find(emp => emp.id === selectedEmployee)

  const filteredEmployees = salesEmployees.filter(employee =>
    employee.full_name.toLowerCase().includes(searchValue.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchValue.toLowerCase())
  )

  const handleSelect = (employeeId: string) => {
    onEmployeeChange(employeeId === selectedEmployee ? 'all' : employeeId)
    setOpen(false)
    setSearchValue('')
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEmployeeChange('all')
  }

  return (
    <div className={cn('relative', className)} dir="rtl">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              'w-full justify-between text-right font-normal h-10 px-3 py-2',
              !selectedEmployeeData && 'text-muted-foreground'
            )}
          >
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
            <div className="flex-1 text-right truncate">
              {selectedEmployeeData ? (
                <div className="flex items-center gap-2 justify-end">
                  <span className="truncate">{selectedEmployeeData.full_name}</span>
                  <User className="h-4 w-4" />
                </div>
              ) : (
                <span>جميع موظفي المبيعات</span>
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="end" dir="rtl">
          <div className="flex items-center border-b px-3 py-2">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              placeholder={placeholder}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className="border-0 focus-visible:ring-0 shadow-none"
            />
          </div>
          <ScrollArea className="max-h-[200px]">
            <div className="p-1">
              {/* All employees option */}
              <div
                className="flex items-center gap-2 px-3 py-2 text-sm cursor-pointer hover:bg-accent rounded-sm"
                onClick={() => handleSelect('all')}
              >
                <Check
                  className={cn(
                    'h-4 w-4',
                    selectedEmployee === 'all' ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <User className="h-4 w-4" />
                <span>جميع موظفي المبيعات</span>
              </div>

              {/* Filtered employees */}
              {filteredEmployees.length === 0 && searchValue ? (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  لا توجد نتائج
                </div>
              ) : (
                filteredEmployees.map((employee) => (
                  <div
                    key={employee.id}
                    className="flex items-center gap-2 px-3 py-2 text-sm cursor-pointer hover:bg-accent rounded-sm"
                    onClick={() => handleSelect(employee.id)}
                  >
                    <Check
                      className={cn(
                        'h-4 w-4',
                        selectedEmployee === employee.id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    <div className="flex flex-col gap-1 flex-1">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="font-medium">{employee.full_name}</span>
                      </div>
                      <span className="text-xs text-muted-foreground">{employee.email}</span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </PopoverContent>
      </Popover>
      {selectedEmployee !== 'all' && (
        <button
          type="button"
          className="absolute left-8 top-1/2 -translate-y-1/2 h-4 w-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none cursor-pointer"
          onClick={handleClear}
        >
          <X className="h-3 w-3" />
          <span className="sr-only">مسح</span>
        </button>
      )}
    </div>
  )
}
