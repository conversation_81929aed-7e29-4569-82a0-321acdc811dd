'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import {
  User,
  Calendar,
  DollarSign,
  Clock,
  TrendingUp,
  TrendingDown,
  Download,
  Search,
  Filter,
  RefreshCw,
  FileSpreadsheet
} from 'lucide-react'
import { exportToCSV, exportToExcel, exportSalaryData } from '@/lib/export-utils'
import type { Database } from '@/lib/supabase'

type SalaryCalculation = Database['public']['Tables']['monthly_salary_calculations']['Row']

interface SalaryRecordsTableProps {
  data: SalaryCalculation[]
  loading: boolean
  onRefresh: () => void
  userRole: string
}

export function SalaryRecordsTable({ data, loading, onRefresh, userRole }: SalaryRecordsTableProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterMonth, setFilterMonth] = useState('all')
  const [filterYear, setFilterYear] = useState('all')
  const [sortField, setSortField] = useState<keyof SalaryCalculation>('calculation_date')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ر.س`
  }

  const getMonthName = (month: number) => {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ]
    return months[month - 1]
  }

  const getEfficiencyBadge = (efficiency: number) => {
    if (efficiency >= 90) {
      return <Badge variant="default" className="bg-green-600">ممتاز</Badge>
    } else if (efficiency >= 80) {
      return <Badge variant="default" className="bg-blue-600">جيد</Badge>
    } else if (efficiency >= 70) {
      return <Badge variant="secondary">مقبول</Badge>
    } else {
      return <Badge variant="destructive">ضعيف</Badge>
    }
  }

  const handleSort = (field: keyof SalaryCalculation) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const getSortIcon = (field: keyof SalaryCalculation) => {
    if (sortField !== field) return null
    return sortDirection === 'asc' ? 
      <TrendingUp className="h-4 w-4" /> : 
      <TrendingDown className="h-4 w-4" />
  }

  // Filter and sort data
  const filteredData = data
    .filter(record => {
      const matchesSearch = !searchTerm || 
        (record.employee as any)?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (record.employee as any)?.email?.toLowerCase().includes(searchTerm.toLowerCase())
      
      const recordDate = new Date(record.calculation_date)
      const matchesMonth = !filterMonth || filterMonth === 'all' || (recordDate.getMonth() + 1).toString() === filterMonth
      const matchesYear = !filterYear || filterYear === 'all' || recordDate.getFullYear().toString() === filterYear

      return matchesSearch && matchesMonth && matchesYear
    })
    .sort((a, b) => {
      const aValue = a[sortField]
      const bValue = b[sortField]
      
      if (aValue === null || aValue === undefined) return 1
      if (bValue === null || bValue === undefined) return -1
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' ? 
          aValue.localeCompare(bValue) : 
          bValue.localeCompare(aValue)
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
      }
      
      return 0
    })

  const handleExportCSV = () => {
    const { columns } = exportSalaryData(filteredData)
    exportToCSV(filteredData, columns, {
      filename: 'salary_records',
      includeTimestamp: true,
      rtlSupport: true
    })
  }

  const handleExportExcel = () => {
    const { columns } = exportSalaryData(filteredData)
    exportToExcel(filteredData, columns, {
      filename: 'salary_records',
      includeTimestamp: true,
      rtlSupport: true
    })
  }

  // Get unique years from data
  const availableYears = [...new Set(data.map(record => 
    new Date(record.calculation_date).getFullYear()
  ))].sort((a, b) => b - a)

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>سجلات الرواتب</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">جاري تحميل سجلات الرواتب...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4" dir="rtl">
      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            سجلات الرواتب
          </CardTitle>
          <CardDescription>
            جميع حسابات الرواتب المحفوظة مع إمكانية البحث والتصفية
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="البحث بالاسم أو البريد الإلكتروني..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>

            {/* Month Filter */}
            <Select value={filterMonth} onValueChange={setFilterMonth}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="الشهر" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الشهور</SelectItem>
                {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                  <SelectItem key={month} value={month.toString()}>
                    {getMonthName(month)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Year Filter */}
            <Select value={filterYear} onValueChange={setFilterYear}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="السنة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع السنوات</SelectItem>
                {availableYears.map(year => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Actions */}
            <div className="flex gap-2">
              <Button variant="outline" onClick={onRefresh} className="gap-2">
                <RefreshCw className="h-4 w-4" />
                تحديث
              </Button>
              <Button variant="outline" onClick={handleExportCSV} className="gap-2">
                <Download className="h-4 w-4" />
                تصدير CSV
              </Button>
              <Button variant="outline" onClick={handleExportExcel} className="gap-2">
                <FileSpreadsheet className="h-4 w-4" />
                تصدير Excel
              </Button>
            </div>
          </div>

          {/* Results Summary */}
          <div className="mb-4">
            <p className="text-sm text-muted-foreground">
              عرض {filteredData.length} من أصل {data.length} سجل
            </p>
          </div>

          {/* Table */}
          {filteredData.length === 0 ? (
            <div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">لا توجد سجلات رواتب</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('employee_id')}
                    >
                      <div className="flex items-center gap-1">
                        الموظف
                        {getSortIcon('employee_id')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('month')}
                    >
                      <div className="flex items-center gap-1">
                        الفترة
                        {getSortIcon('month')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('base_salary')}
                    >
                      <div className="flex items-center gap-1">
                        الراتب الأساسي
                        {getSortIcon('base_salary')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('actual_working_hours')}
                    >
                      <div className="flex items-center gap-1">
                        ساعات العمل
                        {getSortIcon('actual_working_hours')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('total_deductions')}
                    >
                      <div className="flex items-center gap-1">
                        الخصومات
                        {getSortIcon('total_deductions')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('final_salary')}
                    >
                      <div className="flex items-center gap-1">
                        صافي الراتب
                        {getSortIcon('final_salary')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('efficiency')}
                    >
                      <div className="flex items-center gap-1">
                        الكفاءة
                        {getSortIcon('efficiency')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('calculation_date')}
                    >
                      <div className="flex items-center gap-1">
                        تاريخ الحساب
                        {getSortIcon('calculation_date')}
                      </div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{(record.employee as any)?.full_name || 'غير محدد'}</div>
                            <div className="text-sm text-muted-foreground">{(record.employee as any)?.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{getMonthName(record.month)} {record.year}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(record.base_salary)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{record.actual_working_hours?.toFixed(1)} ساعة</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-red-600">
                        {formatCurrency(record.total_deductions || 0)}
                      </TableCell>
                      <TableCell className="font-bold text-green-600">
                        {formatCurrency(record.final_salary)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getEfficiencyBadge(record.efficiency || 0)}
                          <span className="text-sm">{record.efficiency?.toFixed(1)}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(record.calculation_date).toLocaleDateString('ar-SA')}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
