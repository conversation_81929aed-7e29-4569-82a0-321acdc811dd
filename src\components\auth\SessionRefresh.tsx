'use client'

import { useEffect, useRef } from 'react'
import { supabase } from '@/lib/supabase/client'
import { useAuth } from '@/hooks/useAuth'

/**
 * Component to handle session refresh and prevent stale auth state
 */
export function SessionRefresh() {
  const { user, loading } = useAuth()
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastRefreshRef = useRef<number>(0)
  const visibilityTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Don't start refresh cycle if still loading or no user
    if (loading || !user) {
      return
    }

    const startRefreshCycle = () => {
      // Clear any existing interval
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }

      // Refresh session every 5 minutes
      refreshIntervalRef.current = setInterval(async () => {
        const now = Date.now()
        
        // Prevent too frequent refreshes
        if (now - lastRefreshRef.current < 60000) { // 1 minute minimum
          return
        }

        try {
          console.log('Refreshing session...')
          const { error } = await supabase.auth.refreshSession()
          
          if (error) {
            console.error('Session refresh error:', error)
          } else {
            console.log('Session refreshed successfully')
            lastRefreshRef.current = now
          }
        } catch (error) {
          console.error('Session refresh failed:', error)
        }
      }, 5 * 60 * 1000) // 5 minutes
    }

    startRefreshCycle()

    // Also refresh on page visibility change (with throttling)
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const now = Date.now()

        // Clear any existing timeout
        if (visibilityTimeoutRef.current) {
          clearTimeout(visibilityTimeoutRef.current)
        }

        // If page was hidden for more than 5 minutes, refresh after a delay
        if (now - lastRefreshRef.current > 5 * 60 * 1000) {
          visibilityTimeoutRef.current = setTimeout(() => {
            console.log('Page became visible after long absence, refreshing session...')
            supabase.auth.refreshSession()
            lastRefreshRef.current = Date.now()
          }, 1000) // 1 second delay to prevent rapid refreshes
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }
      if (visibilityTimeoutRef.current) {
        clearTimeout(visibilityTimeoutRef.current)
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [user, loading])

  // This component doesn't render anything
  return null
}
