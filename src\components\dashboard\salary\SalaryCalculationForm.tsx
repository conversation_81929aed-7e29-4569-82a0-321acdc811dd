'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Calculator, User, Calendar, DollarSign, AlertTriangle, CheckCircle } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { toast } from '@/hooks/use-toast'
import type { Database } from '@/lib/supabase'

type SalarySettings = Database['public']['Tables']['salary_settings']['Row']
type Profile = Database['public']['Tables']['profiles']['Row']

interface SalaryCalculationFormProps {
  currentSettings: SalarySettings | null
  onCalculationComplete: () => void
  userRole: string
  userAreaId?: string
  userTeamId?: string
}

interface CalculationResult {
  baseSalary: number
  expectedWorkingHours: number
  actualWorkingHours: number
  workingDaysInMonth: number
  actualWorkingDays: number
  proportionalSalary: number
  deficitDeductions: number
  advanceDeductions: number
  totalDeductions: number
  finalSalary: number
  efficiency: number
}

export function SalaryCalculationForm({ 
  currentSettings, 
  onCalculationComplete,
  userRole,
  userAreaId,
  userTeamId
}: SalaryCalculationFormProps) {
  const [employees, setEmployees] = useState<Profile[]>([])
  const [selectedEmployee, setSelectedEmployee] = useState('')
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [loading, setLoading] = useState(false)
  const [calculating, setCalculating] = useState(false)
  const [calculationResult, setCalculationResult] = useState<CalculationResult | null>(null)

  const supabase = createClient()

  useEffect(() => {
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    try {
      let query = supabase
        .from('profiles')
        .select('id, full_name, email, role, area_id, team_id')
        .order('full_name')

      // Apply role-based filtering
      if (userRole === 'team_manager' && userTeamId) {
        query = query.eq('team_id', userTeamId)
      } else if (userRole === 'area_manager' && userAreaId) {
        query = query.eq('area_id', userAreaId)
      }

      const { data, error } = await query

      if (error) throw error
      setEmployees(data || [])
    } catch (error) {
      console.error('Error fetching employees:', error)
      toast.error('حدث خطأ في تحميل قائمة الموظفين')
    }
  }

  const calculateSalary = async () => {
    if (!selectedEmployee || !currentSettings) {
      toast.error('يرجى اختيار موظف والتأكد من وجود إعدادات الراتب')
      return
    }

    try {
      setCalculating(true)

      const response = await fetch('/api/salary/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employee_id: selectedEmployee,
          month: selectedMonth,
          year: selectedYear
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في حساب الراتب')
      }

      setCalculationResult(result.calculation)
      toast.success('تم حساب الراتب بنجاح')

    } catch (error) {
      console.error('Error calculating salary:', error)
      toast.error(error instanceof Error ? error.message : 'حدث خطأ في حساب الراتب')
    } finally {
      setCalculating(false)
    }
  }

  const saveSalaryCalculation = async () => {
    if (!calculationResult || !selectedEmployee) {
      toast.error('لا توجد نتائج حساب للحفظ')
      return
    }

    try {
      setLoading(true)

      const response = await fetch('/api/salary/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employee_id: selectedEmployee,
          month: selectedMonth,
          year: selectedYear,
          save: true
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في حفظ الراتب')
      }

      toast.success('تم حفظ حساب الراتب بنجاح')
      setCalculationResult(null)
      setSelectedEmployee('')
      onCalculationComplete()

    } catch (error) {
      console.error('Error saving salary calculation:', error)
      toast.error(error instanceof Error ? error.message : 'حدث خطأ في حفظ الراتب')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ر.س`
  }

  const getMonthName = (month: number) => {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ]
    return months[month - 1]
  }

  const selectedEmployeeData = employees.find(emp => emp.id === selectedEmployee)

  return (
    <div className="space-y-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            حساب راتب موظف
          </CardTitle>
          <CardDescription>
            احسب راتب موظف لشهر محدد بناءً على ساعات العمل والخصومات
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Employee Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>الموظف</Label>
              <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر موظف" />
                </SelectTrigger>
                <SelectContent>
                  {employees.map(employee => (
                    <SelectItem key={employee.id} value={employee.id}>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        {employee.full_name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>الشهر</Label>
              <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                    <SelectItem key={month} value={month.toString()}>
                      {getMonthName(month)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>السنة</Label>
              <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Selected Employee Info */}
          {selectedEmployeeData && (
            <Card className="bg-muted/50">
              <CardContent className="pt-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <h4 className="font-medium">{selectedEmployeeData.full_name}</h4>
                    <p className="text-sm text-muted-foreground">{selectedEmployeeData.email}</p>
                  </div>
                  <Badge variant="secondary">
                    {selectedEmployeeData.role === 'sales_employee' ? 'موظف مبيعات' : 
                     selectedEmployeeData.role === 'team_manager' ? 'مدير فريق' :
                     selectedEmployeeData.role === 'area_manager' ? 'مدير منطقة' : 'مدير النظام'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Calculate Button */}
          <div className="flex justify-center">
            <Button 
              onClick={calculateSalary}
              disabled={!selectedEmployee || !currentSettings || calculating}
              className="gap-2"
              size="lg"
            >
              <Calculator className="h-4 w-4" />
              {calculating ? 'جاري الحساب...' : 'احسب الراتب'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Calculation Results */}
      {calculationResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              نتائج حساب الراتب
            </CardTitle>
            <CardDescription>
              راتب {selectedEmployeeData?.full_name} لشهر {getMonthName(selectedMonth)} {selectedYear}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatCurrency(calculationResult.baseSalary)}
                </div>
                <div className="text-sm text-muted-foreground">الراتب الأساسي</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {calculationResult.actualWorkingHours.toFixed(1)} ساعة
                </div>
                <div className="text-sm text-muted-foreground">
                  من {calculationResult.expectedWorkingHours.toFixed(1)} ساعة متوقعة
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {calculationResult.efficiency.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">كفاءة العمل</div>
              </div>
            </div>

            <Separator />

            {/* Detailed Breakdown */}
            <div className="space-y-4">
              <h4 className="font-medium">تفاصيل الحساب</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>أيام العمل في الشهر:</span>
                    <span className="font-medium">{calculationResult.workingDaysInMonth} يوم</span>
                  </div>
                  <div className="flex justify-between">
                    <span>أيام العمل الفعلية:</span>
                    <span className="font-medium">{calculationResult.actualWorkingDays} يوم</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الراتب النسبي:</span>
                    <span className="font-medium">{formatCurrency(calculationResult.proportionalSalary)}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>خصومات العجز:</span>
                    <span className="font-medium text-red-600">{formatCurrency(calculationResult.deficitDeductions)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>خصومات السلف:</span>
                    <span className="font-medium text-orange-600">{formatCurrency(calculationResult.advanceDeductions)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>إجمالي الخصومات:</span>
                    <span className="font-medium text-red-600">{formatCurrency(calculationResult.totalDeductions)}</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Final Salary */}
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {formatCurrency(calculationResult.finalSalary)}
              </div>
              <div className="text-lg text-muted-foreground">صافي الراتب</div>
            </div>

            {/* Warnings */}
            {calculationResult.efficiency < 80 && (
              <div className="flex items-center gap-2 text-amber-600 bg-amber-50 p-3 rounded-md">
                <AlertTriangle className="h-4 w-4" />
                <span>تنبيه: كفاءة العمل أقل من 80%</span>
              </div>
            )}

            {calculationResult.totalDeductions > calculationResult.baseSalary * 0.3 && (
              <div className="flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md">
                <AlertTriangle className="h-4 w-4" />
                <span>تحذير: الخصومات تتجاوز 30% من الراتب الأساسي</span>
              </div>
            )}

            {/* Save Button */}
            <div className="flex justify-center">
              <Button
                onClick={saveSalaryCalculation}
                disabled={loading}
                className="gap-2"
                size="lg"
              >
                <DollarSign className="h-4 w-4" />
                {loading ? 'جاري الحفظ...' : 'حفظ حساب الراتب'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
