import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Get authenticated user
    const { data: authData, error: authError } = await supabase.auth.getUser()
    
    if (authError || !authData.user) {
      return NextResponse.json({
        error: 'Authentication required'
      }, { status: 401 })
    }

    const userId = authData.user.id
    
    // Get user profile to determine role and permissions
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, role, area_id, team_id')
      .eq('id', userId)
      .single()
    
    if (profileError || !profile) {
      return NextResponse.json({
        error: 'Profile not found'
      }, { status: 404 })
    }

    // Fetch employees based on user role
    let employeesQuery = supabase
      .from('profiles')
      .select('id, full_name, email, role, area_id, team_id')

    // Apply role-based filtering
    if (profile.role === 'sales_employee') {
      employeesQuery = employeesQuery.eq('id', userId)
    } else if (profile.role === 'team_manager' && profile.team_id) {
      employeesQuery = employeesQuery.eq('team_id', profile.team_id)
    } else if (profile.role === 'area_manager' && profile.area_id) {
      employeesQuery = employeesQuery.eq('area_id', profile.area_id)
    }

    const { data: employeesData, error: employeesError } = await employeesQuery.order('full_name')

    if (employeesError) {
      console.error('Employees query error:', employeesError)
      return NextResponse.json({
        error: 'Failed to fetch employees',
        details: employeesError.message
      }, { status: 500 })
    }

    let areasData = []
    let teamsData = []

    // Fetch areas and teams for system admins and area managers
    if (profile.role === 'system_admin') {
      const [areasResult, teamsResult] = await Promise.all([
        supabase.from('areas').select('id, name').order('name'),
        supabase.from('teams').select('id, name, area_id').order('name')
      ])

      if (areasResult.error) {
        console.error('Areas query error:', areasResult.error)
      } else {
        areasData = areasResult.data || []
      }

      if (teamsResult.error) {
        console.error('Teams query error:', teamsResult.error)
      } else {
        teamsData = teamsResult.data || []
      }
    } else if (profile.role === 'area_manager' && profile.area_id) {
      // Area managers can see teams in their area
      const teamsResult = await supabase
        .from('teams')
        .select('id, name, area_id')
        .eq('area_id', profile.area_id)
        .order('name')

      if (teamsResult.error) {
        console.error('Teams query error:', teamsResult.error)
      } else {
        teamsData = teamsResult.data || []
      }

      // Also get the area info
      const areaResult = await supabase
        .from('areas')
        .select('id, name')
        .eq('id', profile.area_id)
        .single()

      if (!areaResult.error && areaResult.data) {
        areasData = [areaResult.data]
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        employees: employeesData || [],
        areas: areasData,
        teams: teamsData,
        userRole: profile.role
      }
    })
    
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
