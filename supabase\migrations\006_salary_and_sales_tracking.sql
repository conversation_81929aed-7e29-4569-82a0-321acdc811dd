-- Employee Salary and Sales Tracking System
-- This migration creates tables for comprehensive salary calculation and sales tracking

-- Create salary_settings table for configurable salary parameters
CREATE TABLE IF NOT EXISTS public.salary_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    base_salary DECIMAL(10,2) NOT NULL DEFAULT 3000.00 CHECK (base_salary >= 0),
    working_hours_per_day INTEGER NOT NULL DEFAULT 8 CHECK (working_hours_per_day > 0),
    currency TEXT NOT NULL DEFAULT 'SAR',
    effective_from DATE NOT NULL DEFAULT CURRENT_DATE,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create employee_advances table for tracking salary advances (السلف)
CREATE TABLE IF NOT EXISTS public.employee_advances (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    employee_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    advance_date DATE NOT NULL DEFAULT CURRENT_DATE,
    reason TEXT,
    status TEXT CHECK (status IN ('pending', 'approved', 'deducted', 'cancelled')) DEFAULT 'pending',
    approved_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    approved_at TIMESTAMP WITH TIME ZONE,
    deducted_from_month DATE, -- Which month's salary this was deducted from
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create monthly_salary_calculations table for storing calculated salaries
CREATE TABLE IF NOT EXISTS public.monthly_salary_calculations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    employee_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    salary_month DATE NOT NULL, -- First day of the month (e.g., 2024-01-01 for January 2024)
    base_salary DECIMAL(10,2) NOT NULL CHECK (base_salary >= 0),
    expected_working_hours INTEGER NOT NULL CHECK (expected_working_hours > 0),
    actual_working_hours DECIMAL(8,2) NOT NULL DEFAULT 0 CHECK (actual_working_hours >= 0),
    working_days_in_month INTEGER NOT NULL CHECK (working_days_in_month > 0),
    days_worked INTEGER NOT NULL DEFAULT 0 CHECK (days_worked >= 0),
    total_sales_amount DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (total_sales_amount >= 0),
    deficit_amount DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (deficit_amount >= 0),
    advances_deducted DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (advances_deducted >= 0),
    other_deductions DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (other_deductions >= 0),
    gross_salary DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (gross_salary >= 0),
    net_salary DECIMAL(10,2) NOT NULL DEFAULT 0,
    working_hours_efficiency DECIMAL(5,2) NOT NULL DEFAULT 0 CHECK (working_hours_efficiency >= 0),
    calculation_date TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    calculated_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    is_finalized BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(employee_id, salary_month)
);

-- Create salary_deductions table for tracking various deductions
CREATE TABLE IF NOT EXISTS public.salary_deductions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    monthly_salary_id UUID REFERENCES public.monthly_salary_calculations(id) ON DELETE CASCADE NOT NULL,
    deduction_type TEXT CHECK (deduction_type IN ('deficit', 'advance', 'penalty', 'other')) NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    description TEXT NOT NULL,
    reference_id UUID, -- Can reference employee_advances.id or other tables
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Add missing columns to daily_closings table for enhanced tracking
ALTER TABLE public.daily_closings 
ADD COLUMN IF NOT EXISTS advances_amount DECIMAL(10,2) DEFAULT 0 CHECK (advances_amount >= 0),
ADD COLUMN IF NOT EXISTS price_breaks_amount DECIMAL(10,2) DEFAULT 0 CHECK (price_breaks_amount >= 0);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_salary_settings_active ON public.salary_settings(is_active, effective_from);
CREATE INDEX IF NOT EXISTS idx_employee_advances_employee ON public.employee_advances(employee_id, advance_date);
CREATE INDEX IF NOT EXISTS idx_employee_advances_status ON public.employee_advances(status);
CREATE INDEX IF NOT EXISTS idx_monthly_salary_employee_month ON public.monthly_salary_calculations(employee_id, salary_month);
CREATE INDEX IF NOT EXISTS idx_monthly_salary_month ON public.monthly_salary_calculations(salary_month);
CREATE INDEX IF NOT EXISTS idx_salary_deductions_monthly_salary ON public.salary_deductions(monthly_salary_id);
CREATE INDEX IF NOT EXISTS idx_daily_closings_user_date ON public.daily_closings(user_id, closing_date);

-- Create updated_at triggers for new tables
CREATE TRIGGER handle_salary_settings_updated_at
    BEFORE UPDATE ON public.salary_settings
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_employee_advances_updated_at
    BEFORE UPDATE ON public.employee_advances
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_monthly_salary_calculations_updated_at
    BEFORE UPDATE ON public.monthly_salary_calculations
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Enable Row Level Security
ALTER TABLE public.salary_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employee_advances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.monthly_salary_calculations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.salary_deductions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for salary_settings table
-- System admins can manage salary settings
CREATE POLICY "System admins can manage salary settings" ON public.salary_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- All authenticated users can view active salary settings
CREATE POLICY "Users can view active salary settings" ON public.salary_settings
    FOR SELECT USING (
        is_active = true AND
        auth.uid() IS NOT NULL
    );

-- RLS Policies for employee_advances table
-- System admins can manage all advances
CREATE POLICY "System admins can manage all advances" ON public.employee_advances
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- Area managers can manage advances for their area employees
CREATE POLICY "Area managers can manage area advances" ON public.employee_advances
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles p1
            JOIN public.profiles p2 ON p2.area_id = p1.area_id
            WHERE p1.id = auth.uid() AND p1.role = 'area_manager'
            AND p2.id = employee_id
        )
    );

-- Team managers can manage advances for their team employees
CREATE POLICY "Team managers can manage team advances" ON public.employee_advances
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles p1
            JOIN public.profiles p2 ON p2.team_id = p1.team_id
            WHERE p1.id = auth.uid() AND p1.role = 'team_manager'
            AND p2.id = employee_id
        )
    );

-- Employees can view their own advances
CREATE POLICY "Employees can view own advances" ON public.employee_advances
    FOR SELECT USING (employee_id = auth.uid());

-- RLS Policies for monthly_salary_calculations table
-- System admins can manage all salary calculations
CREATE POLICY "System admins can manage all salary calculations" ON public.monthly_salary_calculations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- Area managers can view salary calculations for their area employees
CREATE POLICY "Area managers can view area salary calculations" ON public.monthly_salary_calculations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles p1
            JOIN public.profiles p2 ON p2.area_id = p1.area_id
            WHERE p1.id = auth.uid() AND p1.role = 'area_manager'
            AND p2.id = employee_id
        )
    );

-- Team managers can view salary calculations for their team employees
CREATE POLICY "Team managers can view team salary calculations" ON public.monthly_salary_calculations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles p1
            JOIN public.profiles p2 ON p2.team_id = p1.team_id
            WHERE p1.id = auth.uid() AND p1.role = 'team_manager'
            AND p2.id = employee_id
        )
    );

-- Employees can view their own salary calculations
CREATE POLICY "Employees can view own salary calculations" ON public.monthly_salary_calculations
    FOR SELECT USING (employee_id = auth.uid());

-- RLS Policies for salary_deductions table
-- Inherit permissions from monthly_salary_calculations
CREATE POLICY "Users can view salary deductions based on salary access" ON public.salary_deductions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.monthly_salary_calculations msc
            WHERE msc.id = monthly_salary_id
            AND (
                -- System admins can see all
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND role = 'system_admin'
                )
                OR
                -- Area managers can see their area
                EXISTS (
                    SELECT 1 FROM public.profiles p1
                    JOIN public.profiles p2 ON p2.area_id = p1.area_id
                    WHERE p1.id = auth.uid() AND p1.role = 'area_manager'
                    AND p2.id = msc.employee_id
                )
                OR
                -- Team managers can see their team
                EXISTS (
                    SELECT 1 FROM public.profiles p1
                    JOIN public.profiles p2 ON p2.team_id = p1.team_id
                    WHERE p1.id = auth.uid() AND p1.role = 'team_manager'
                    AND p2.id = msc.employee_id
                )
                OR
                -- Employees can see their own
                msc.employee_id = auth.uid()
            )
        )
    );

-- System admins can manage salary deductions
CREATE POLICY "System admins can manage salary deductions" ON public.salary_deductions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- Insert default salary settings
INSERT INTO public.salary_settings (base_salary, working_hours_per_day, currency, effective_from, is_active)
VALUES (3000.00, 8, 'SAR', CURRENT_DATE, true)
ON CONFLICT DO NOTHING;
