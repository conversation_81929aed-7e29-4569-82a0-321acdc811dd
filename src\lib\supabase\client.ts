import { createBrowserClient } from '@supabase/ssr'
import type { Database } from '../supabase'

// Singleton instance to prevent multiple client creation
let supabaseClient: ReturnType<typeof createBrowserClient<Database>> | null = null

export function createClient() {
  // Return existing instance if available
  if (supabaseClient) {
    return supabaseClient
  }

  // Create new instance only if none exists
  supabaseClient = createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  return supabaseClient
}

// Export the singleton instance directly for consistency
export const supabase = createClient()
