"use client"

import * as React from "react"
import { usePathname } from "next/navigation"


import { SearchBar } from "@/components/search/SearchBar"
import { NotificationBell } from "@/components/ui/notification-bell"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import { AppSidebar } from "./app-sidebar"
import { PageTransition } from "@/components/ui/page-transition"
import { ErrorBoundary } from "@/components/ui/error-boundary"
import { QuickLoading } from "@/components/ui/loading"
import { useAuthSync } from "@/hooks/useAuthSync"

interface DashboardLayoutProps {
  children: React.ReactNode
  user?: {
    name?: string
    email?: string
    avatar?: string
    role?: string
  } | null
}

// Breadcrumb mapping for Arabic pages
const breadcrumbMap: Record<string, string> = {
  '/dashboard': 'الرئيسية',
  '/dashboard/projects': 'المشاريع',
  '/dashboard/projects/active': 'المشاريع النشطة',
  '/dashboard/projects/completed': 'المشاريع المكتملة',
  '/dashboard/projects/new': 'مشروع جديد',
  '/dashboard/tasks': 'المهام',
  '/dashboard/tickets': 'الطلبات',
  '/dashboard/tickets/new': 'طلب جديد',
  '/dashboard/tickets/open': 'الطلبات النشطة',
  '/dashboard/tickets/closed': 'الطلبات المغلقة',
  '/dashboard/profile': 'الملف الشخصي',
  '/dashboard/profile/edit': 'تعديل الملف الشخصي',
  '/dashboard/daily-closing': 'التقفيل اليومي',
  '/dashboard/calendar': 'التقويم',
  '/dashboard/messages': 'الرسائل',
  '/dashboard/settings': 'الإعدادات',
  '/dashboard/users': 'إدارة المستخدمين',
  '/dashboard/users/new': 'إضافة مستخدم',
  '/dashboard/users/roles': 'الأدوار والصلاحيات',
  '/dashboard/areas': 'إدارة المناطق',
  '/dashboard/teams': 'إدارة الفرق',
  '/dashboard/assign-users': 'تعيين المستخدمين',
  '/dashboard/packages': 'إدارة الباقات',
  '/dashboard/notifications': 'الإشعارات',
  '/dashboard/reports': 'التقارير',
  '/dashboard/reports/activity': 'تقارير النشاط',
  '/dashboard/reports/users': 'تقارير المستخدمين',
  '/dashboard/reports/system': 'إحصائيات النظام',
  '/dashboard/projects/archived': 'المشاريع المؤرشفة',
  '/dashboard/settings/system': 'إعدادات النظام',
  '/dashboard/settings/security': 'إعدادات الأمان',
  '/dashboard/settings/backup': 'النسخ الاحتياطي',
}

function generateBreadcrumbs(pathname: string) {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs = []

  let currentPath = ''
  for (const segment of segments) {
    currentPath += `/${segment}`
    let title = breadcrumbMap[currentPath] || segment

    // Handle dynamic ticket IDs
    if (segments[segments.length - 2] === 'tickets' && segments[segments.length - 1] !== 'new' && segments[segments.length - 1] !== 'open' && segments[segments.length - 1] !== 'all') {
      title = 'تفاصيل الطلب'
    }

    // Handle dynamic profile IDs
    if (segments[segments.length - 2] === 'profile') {
      title = 'الملف الشخصي'
    }

    // Handle dynamic user IDs
    if (segments[segments.length - 2] === 'users' && segments[segments.length - 1] !== 'new' && segments[segments.length - 1] !== 'roles') {
      title = 'ملف المستخدم'
    }

    breadcrumbs.push({
      title,
      href: currentPath,
      isLast: currentPath === pathname
    })
  }

  return breadcrumbs
}

export function DashboardLayout({ children, user }: DashboardLayoutProps) {
  const pathname = usePathname()
  const breadcrumbs = generateBreadcrumbs(pathname)
  // const isAdmin = user?.role === 'system_admin' // Unused variable

  // Sync auth state to prevent stale state issues
  useAuthSync()

  return (
    <SidebarProvider>
      <AppSidebar user={user} />
      <SidebarInset>
        {/* Header */}
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear" dir="rtl">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-mr-1" />
            <Separator orientation="vertical" className="ml-2 h-4" />
            
            {/* Breadcrumbs */}
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((breadcrumb, index) => (
                  <React.Fragment key={breadcrumb.href}>
                    <BreadcrumbItem className="hidden md:block">
                      {breadcrumb.isLast ? (
                        <BreadcrumbPage>{breadcrumb.title}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink href={breadcrumb.href}>
                          {breadcrumb.title}
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && (
                      <BreadcrumbSeparator className="hidden md:block" />
                    )}
                  </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          {/* Header Actions */}
          <div className="mr-auto flex items-center gap-2 px-4">
            {/* Enhanced Search */}
            <SearchBar
              placeholder="البحث في النظام..."
              showMobileButton={true}
            />

            {/* Notifications */}
            <NotificationBell />
          </div>
        </header>

        {/* Main Content */}
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0 overflow-hidden" dir="rtl">
          <div className="w-full max-w-full overflow-x-hidden">
            <ErrorBoundary>
              <React.Suspense fallback={<QuickLoading text="جاري تحميل المحتوى..." />}>
                <PageTransition>
                  {children}
                </PageTransition>
              </React.Suspense>
            </ErrorBoundary>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
