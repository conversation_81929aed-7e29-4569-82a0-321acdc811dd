import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseClient } from '@/lib/supabase/server'
import { requireAuth } from '@/lib/auth'

// Helper function to calculate working days in a month
function getWorkingDaysInMonth(year: number, month: number): number {
  const date = new Date(year, month - 1, 1)
  const lastDay = new Date(year, month, 0).getDate()
  let workingDays = 0

  for (let day = 1; day <= lastDay; day++) {
    date.setDate(day)
    const dayOfWeek = date.getDay()
    // Assuming Friday (5) and Saturday (6) are weekends in Saudi Arabia
    if (dayOfWeek !== 5 && dayOfWeek !== 6) {
      workingDays++
    }
  }

  return workingDays
}

// Helper function to calculate working hours from attendance records
async function calculateWorkingHours(supabase: any, employeeId: string, year: number, month: number) {
  const startDate = `${year}-${month.toString().padStart(2, '0')}-01`
  const endDate = new Date(year, month, 0).toISOString().split('T')[0]

  const { data: dailyClosings, error } = await supabase
    .from('daily_closings')
    .select(`
      closing_date,
      attendance_records(check_in_time, check_out_time)
    `)
    .eq('user_id', employeeId)
    .gte('closing_date', startDate)
    .lte('closing_date', endDate)
    .eq('attendance_submitted', true)
    .eq('departure_submitted', true)

  if (error) throw error

  let totalHours = 0
  let daysWorked = 0

  for (const closing of dailyClosings || []) {
    if (closing.attendance_records && closing.attendance_records.length > 0) {
      const record = closing.attendance_records[0]
      if (record.check_in_time && record.check_out_time) {
        const checkIn = new Date(`2000-01-01T${record.check_in_time}`)
        let checkOut = new Date(`2000-01-01T${record.check_out_time}`)

        // Handle next day checkout
        if (checkOut < checkIn) {
          checkOut.setDate(checkOut.getDate() + 1)
        }

        const diffMs = checkOut.getTime() - checkIn.getTime()
        const diffHours = diffMs / (1000 * 60 * 60)

        totalHours += diffHours
        daysWorked++
      }
    }
  }

  return { totalHours, daysWorked }
}

// Helper function to calculate total sales for the month
async function calculateMonthlySales(supabase: any, employeeId: string, year: number, month: number) {
  const startDate = `${year}-${month.toString().padStart(2, '0')}-01`
  const endDate = new Date(year, month, 0).toISOString().split('T')[0]

  const { data: dailyClosings, error } = await supabase
    .from('daily_closings')
    .select('total_sales_amount, deficit_amount')
    .eq('user_id', employeeId)
    .gte('closing_date', startDate)
    .lte('closing_date', endDate)
    .eq('sales_submitted', true)

  if (error) throw error

  const totalSales = dailyClosings?.reduce((sum, closing) => sum + (closing.total_sales_amount || 0), 0) || 0
  const totalDeficit = dailyClosings?.reduce((sum, closing) => sum + (closing.deficit_amount || 0), 0) || 0

  return { totalSales, totalDeficit }
}

// Helper function to calculate advances for the month
async function calculateAdvancesDeducted(supabase: any, employeeId: string, year: number, month: number) {
  const monthDate = `${year}-${month.toString().padStart(2, '0')}-01`

  const { data: advances, error } = await supabase
    .from('employee_advances')
    .select('amount')
    .eq('employee_id', employeeId)
    .eq('status', 'deducted')
    .eq('deducted_from_month', monthDate)

  if (error) throw error

  return advances?.reduce((sum, advance) => sum + advance.amount, 0) || 0
}

// POST - Calculate monthly salary for an employee
export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    const supabase = await createSupabaseClient()

    const body = await request.json()
    const { employee_id, year, month } = body

    // Validate input
    if (!employee_id || !year || !month) {
      return NextResponse.json(
        { error: 'معرف الموظف والسنة والشهر مطلوبة' },
        { status: 400 }
      )
    }

    // Check permissions - only system admins can calculate salaries for others
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'system_admin' && employee_id !== user.id) {
      return NextResponse.json(
        { error: 'غير مصرح لك بحساب راتب هذا الموظف' },
        { status: 403 }
      )
    }

    // Get current salary settings
    const { data: salarySettings, error: settingsError } = await supabase
      .from('salary_settings')
      .select('*')
      .eq('is_active', true)
      .order('effective_from', { ascending: false })
      .limit(1)
      .single()

    if (settingsError || !salarySettings) {
      return NextResponse.json(
        { error: 'لم يتم العثور على إعدادات الراتب' },
        { status: 404 }
      )
    }

    // Calculate working days and expected hours
    const workingDaysInMonth = getWorkingDaysInMonth(year, month)
    const expectedWorkingHours = workingDaysInMonth * salarySettings.working_hours_per_day

    // Calculate actual working hours and days worked
    const { totalHours: actualWorkingHours, daysWorked } = await calculateWorkingHours(
      supabase, employee_id, year, month
    )

    // Calculate monthly sales and deficit
    const { totalSales, totalDeficit } = await calculateMonthlySales(
      supabase, employee_id, year, month
    )

    // Calculate advances deducted
    const advancesDeducted = await calculateAdvancesDeducted(
      supabase, employee_id, year, month
    )

    // Calculate salary
    const baseSalary = salarySettings.base_salary
    const workingHoursEfficiency = expectedWorkingHours > 0
      ? Math.min((actualWorkingHours / expectedWorkingHours) * 100, 100)
      : 0

    // Proportional salary based on hours worked
    const grossSalary = expectedWorkingHours > 0
      ? (baseSalary * actualWorkingHours) / expectedWorkingHours
      : 0

    // Calculate net salary after deductions
    const totalDeductions = totalDeficit + advancesDeducted
    const netSalary = Math.max(grossSalary - totalDeductions, 0)

    const salaryMonth = `${year}-${month.toString().padStart(2, '0')}-01`

    // Save or update salary calculation
    const salaryData = {
      employee_id,
      salary_month: salaryMonth,
      base_salary: baseSalary,
      expected_working_hours: expectedWorkingHours,
      actual_working_hours: actualWorkingHours,
      working_days_in_month: workingDaysInMonth,
      days_worked: daysWorked,
      total_sales_amount: totalSales,
      deficit_amount: totalDeficit,
      advances_deducted: advancesDeducted,
      other_deductions: 0,
      gross_salary: grossSalary,
      net_salary: netSalary,
      working_hours_efficiency: workingHoursEfficiency,
      calculated_by: user.id,
      is_finalized: false
    }

    const { data: salaryCalculation, error: saveError } = await supabase
      .from('monthly_salary_calculations')
      .upsert(salaryData, { onConflict: 'employee_id,salary_month' })
      .select()
      .single()

    if (saveError) {
      console.error('Error saving salary calculation:', saveError)
      return NextResponse.json(
        { error: 'حدث خطأ في حفظ حساب الراتب' },
        { status: 500 }
      )
    }

    // Create deduction records
    if (salaryCalculation) {
      const deductions = []

      if (totalDeficit > 0) {
        deductions.push({
          monthly_salary_id: salaryCalculation.id,
          deduction_type: 'deficit',
          amount: totalDeficit,
          description: 'العجز المتراكم للشهر'
        })
      }

      if (advancesDeducted > 0) {
        deductions.push({
          monthly_salary_id: salaryCalculation.id,
          deduction_type: 'advance',
          amount: advancesDeducted,
          description: 'السلف المستقطعة'
        })
      }

      if (deductions.length > 0) {
        // Delete existing deductions first
        await supabase
          .from('salary_deductions')
          .delete()
          .eq('monthly_salary_id', salaryCalculation.id)

        // Insert new deductions
        await supabase
          .from('salary_deductions')
          .insert(deductions)
      }
    }

    return NextResponse.json({
      success: true,
      data: salaryCalculation
    })

  } catch (error: unknown) {
    console.error('Salary calculation error:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// GET - Fetch salary calculation for an employee
export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()
    const supabase = await createSupabaseClient()

    const searchParams = request.nextUrl.searchParams
    const employee_id = searchParams.get('employee_id')
    const month = searchParams.get('month')
    const year = searchParams.get('year')

    if (!employee_id || !month || !year) {
      return NextResponse.json({ error: 'معرف الموظف والشهر والسنة مطلوبة' }, { status: 400 })
    }

    // Check permissions - only system admins can view salaries for others
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'system_admin' && employee_id !== user.id) {
      return NextResponse.json(
        { error: 'غير مصرح لك بعرض راتب هذا الموظف' },
        { status: 403 }
      )
    }

    const salaryMonth = `${year}-${month.toString().padStart(2, '0')}-01`

    // Get salary record with deductions
    const { data: salaryRecord, error: fetchError } = await supabase
      .from('monthly_salary_calculations')
      .select(`
        *,
        employee:profiles!monthly_salary_calculations_employee_id_fkey(
          id, full_name, email, role
        ),
        deductions:salary_deductions(
          id, deduction_type, amount, description
        )
      `)
      .eq('employee_id', employee_id)
      .eq('salary_month', salaryMonth)
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        // No record found, return null
        return NextResponse.json({ salary: null })
      }
      console.error('Error fetching salary record:', fetchError)
      return NextResponse.json({ error: 'خطأ في جلب بيانات الراتب' }, { status: 500 })
    }

    return NextResponse.json({ salary: salaryRecord })

  } catch (error: unknown) {
    console.error('Error in salary fetch API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
