'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts'
import { format, parseISO } from 'date-fns'
import { ar } from 'date-fns/locale'

interface SalesData {
  id: string
  closing_date: string
  user_id: string
  total_sales_amount: number
  cash_delivered: number
  deficit_amount: number
  advances_amount: number
  price_breaks_amount: number
  working_hours?: number
  employee: {
    id: string
    full_name: string
    email: string
    role: string
    area_id?: string
    team_id?: string
  }
  area?: {
    id: string
    name: string
  }
  team?: {
    id: string
    name: string
  }
}

interface SalesChartsProps {
  data: SalesData[]
  loading: boolean
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export function SalesCharts({ data, loading }: SalesChartsProps) {
  if (loading) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-6 bg-muted rounded animate-pulse mb-2"></div>
              <div className="h-4 bg-muted rounded animate-pulse w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-muted rounded animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>الرسوم البيانية</CardTitle>
          <CardDescription>تحليل بصري لبيانات المبيعات</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">لا توجد بيانات لعرض الرسوم البيانية</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Prepare daily sales trend data
  const dailySalesData = data
    .sort((a, b) => new Date(a.closing_date).getTime() - new Date(b.closing_date).getTime())
    .reduce((acc, item) => {
      const date = item.closing_date
      const existing = acc.find(d => d.date === date)
      
      if (existing) {
        existing.sales += item.total_sales_amount
        existing.deficit += item.deficit_amount
        existing.hours += item.working_hours || 0
      } else {
        acc.push({
          date,
          sales: item.total_sales_amount,
          deficit: item.deficit_amount,
          hours: item.working_hours || 0,
          formattedDate: format(parseISO(date), 'dd/MM', { locale: ar })
        })
      }
      
      return acc
    }, [] as any[])

  // Prepare employee performance data
  const employeePerformanceData = Object.values(
    data.reduce((acc, item) => {
      const employeeId = item.user_id
      if (!acc[employeeId]) {
        acc[employeeId] = {
          name: item.employee.full_name,
          sales: 0,
          hours: 0,
          deficit: 0,
          count: 0
        }
      }
      
      acc[employeeId].sales += item.total_sales_amount
      acc[employeeId].hours += item.working_hours || 0
      acc[employeeId].deficit += item.deficit_amount
      acc[employeeId].count += 1
      
      return acc
    }, {} as Record<string, any>)
  )
  .sort((a, b) => b.sales - a.sales)
  .slice(0, 10) // Top 10 employees

  // Prepare area/team performance data
  const areaPerformanceData = Object.values(
    data.reduce((acc, item) => {
      const areaName = item.area?.name || 'غير محدد'
      if (!acc[areaName]) {
        acc[areaName] = {
          name: areaName,
          sales: 0,
          count: 0
        }
      }
      
      acc[areaName].sales += item.total_sales_amount
      acc[areaName].count += 1
      
      return acc
    }, {} as Record<string, any>)
  )

  // Prepare efficiency data (sales per hour)
  const efficiencyData = employeePerformanceData
    .filter(emp => emp.hours > 0)
    .map(emp => ({
      name: emp.name,
      efficiency: emp.sales / emp.hours,
      sales: emp.sales,
      hours: emp.hours
    }))
    .sort((a, b) => b.efficiency - a.efficiency)
    .slice(0, 8)

  const formatCurrency = (value: number) => {
    return `${value.toLocaleString('ar-SA')} ر.س`
  }

  const formatHours = (value: number) => {
    return `${value.toFixed(1)} ساعة`
  }

  return (
    <div className="grid gap-6">
      {/* Daily Sales Trend */}
      <Card>
        <CardHeader>
          <CardTitle>اتجاه المبيعات اليومية</CardTitle>
          <CardDescription>تتبع المبيعات والعجز عبر الوقت</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={dailySalesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="formattedDate" 
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `${(value / 1000).toFixed(0)}ك`}
              />
              <Tooltip 
                formatter={(value: number, name: string) => [
                  formatCurrency(value),
                  name === 'sales' ? 'المبيعات' : name === 'deficit' ? 'العجز' : 'ساعات العمل'
                ]}
                labelFormatter={(label) => `التاريخ: ${label}`}
              />
              <Line 
                type="monotone" 
                dataKey="sales" 
                stroke="#0088FE" 
                strokeWidth={2}
                name="sales"
              />
              <Line 
                type="monotone" 
                dataKey="deficit" 
                stroke="#FF8042" 
                strokeWidth={2}
                name="deficit"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Top Employees Performance */}
        <Card>
          <CardHeader>
            <CardTitle>أداء أفضل الموظفين</CardTitle>
            <CardDescription>أعلى 10 موظفين من حيث المبيعات</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={employeePerformanceData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  type="number"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `${(value / 1000).toFixed(0)}ك`}
                />
                <YAxis 
                  type="category"
                  dataKey="name" 
                  tick={{ fontSize: 10 }}
                  width={80}
                />
                <Tooltip 
                  formatter={(value: number) => [formatCurrency(value), 'المبيعات']}
                />
                <Bar dataKey="sales" fill="#0088FE" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Area Performance */}
        <Card>
          <CardHeader>
            <CardTitle>أداء المناطق</CardTitle>
            <CardDescription>توزيع المبيعات حسب المنطقة</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={areaPerformanceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="sales"
                >
                  {areaPerformanceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [formatCurrency(value), 'المبيعات']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Sales Efficiency */}
        <Card>
          <CardHeader>
            <CardTitle>كفاءة المبيعات</CardTitle>
            <CardDescription>المبيعات لكل ساعة عمل</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={efficiencyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  tick={{ fontSize: 10 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `${value.toFixed(0)}`}
                />
                <Tooltip 
                  formatter={(value: number) => [formatCurrency(value), 'ر.س/ساعة']}
                />
                <Bar dataKey="efficiency" fill="#00C49F" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Working Hours vs Sales */}
        <Card>
          <CardHeader>
            <CardTitle>ساعات العمل مقابل المبيعات</CardTitle>
            <CardDescription>العلاقة بين ساعات العمل والمبيعات</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={dailySalesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="formattedDate" 
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  yAxisId="sales"
                  orientation="right"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `${(value / 1000).toFixed(0)}ك`}
                />
                <YAxis 
                  yAxisId="hours"
                  orientation="left"
                  tick={{ fontSize: 12 }}
                />
                <Tooltip 
                  formatter={(value: number, name: string) => [
                    name === 'sales' ? formatCurrency(value) : formatHours(value),
                    name === 'sales' ? 'المبيعات' : 'ساعات العمل'
                  ]}
                />
                <Bar yAxisId="hours" dataKey="hours" fill="#FFBB28" name="hours" />
                <Bar yAxisId="sales" dataKey="sales" fill="#0088FE" name="sales" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Summary Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>إحصائيات ملخصة</CardTitle>
          <CardDescription>نظرة عامة على الأداء</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {employeePerformanceData.length}
              </div>
              <div className="text-sm text-muted-foreground">موظف نشط</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(data.reduce((sum, item) => sum + item.total_sales_amount, 0))}
              </div>
              <div className="text-sm text-muted-foreground">إجمالي المبيعات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {(data.reduce((sum, item) => sum + (item.working_hours || 0), 0)).toFixed(1)}
              </div>
              <div className="text-sm text-muted-foreground">إجمالي ساعات العمل</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(data.reduce((sum, item) => sum + item.deficit_amount, 0))}
              </div>
              <div className="text-sm text-muted-foreground">إجمالي العجز</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
