'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
  Eye,
  TrendingUp,
  TrendingDown,
  Minus,
  User,
  Users,
  Calendar,
  DollarSign,
  Clock,
  AlertTriangle,
  Search,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

interface SalesData {
  id: string
  closing_date: string
  user_id: string
  total_sales_amount: number
  cash_delivered: number
  deficit_amount: number
  advances_amount: number
  price_breaks_amount: number
  working_hours?: number
  employee: {
    id: string
    full_name: string
    email: string
    role: string
    area_id?: string
    team_id?: string
  }
  area?: {
    id: string
    name: string
  }
  team?: {
    id: string
    name: string
  }
}

interface EnhancedSalesDataTableProps {
  data: SalesData[]
  loading: boolean
}

const ITEMS_PER_PAGE_OPTIONS = [10, 25, 50, 100]

export function EnhancedSalesDataTable({ data, loading }: EnhancedSalesDataTableProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [sortField, setSortField] = useState<keyof SalesData>('closing_date')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [searchQuery, setSearchQuery] = useState('')

  // Filter data based on search
  const filteredData = data.filter(item =>
    item.employee.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    format(new Date(item.closing_date), 'dd/MM/yyyy', { locale: ar }).includes(searchQuery)
  )

  // Sort data
  const sortedData = [...filteredData].sort((a, b) => {
    let aValue: any = a[sortField]
    let bValue: any = b[sortField]

    // Handle nested properties
    if (sortField === 'employee') {
      aValue = a.employee.full_name
      bValue = b.employee.full_name
    }

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  // Paginate data
  const totalPages = Math.ceil(sortedData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedData = sortedData.slice(startIndex, endIndex)

  const handleSort = (field: keyof SalesData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  // للمبالغ المالية الفعلية - دقة كاملة مع تحسين العرض
  const formatCurrency = (amount: number) => {
    const formatted = amount.toLocaleString('ar-SA', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    })
    return `${formatted} ر.س`
  }

  // للعروض المدمجة والرسوم البيانية - اختصارات مقبولة
  const formatCompactCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)} مليون`
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(0)} ألف`
    } else {
      return `${amount.toFixed(0)}`
    }
  }

  // تنسيق الأرقام الكبيرة مع فواصل واضحة
  const formatLargeNumber = (amount: number) => {
    return amount.toLocaleString('ar-SA')
  }

  // تنسيق النسب المئوية
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy', { locale: ar })
  }

  const formatTime = (hours: number) => {
    const wholeHours = Math.floor(hours)
    const minutes = Math.round((hours - wholeHours) * 60)
    return `${wholeHours}:${minutes.toString().padStart(2, '0')}`
  }

  const getPerformanceIndicator = (item: SalesData) => {
    const efficiency = item.working_hours ? item.total_sales_amount / item.working_hours : 0
    
    if (efficiency > 100) {
      return { icon: <TrendingUp className="h-4 w-4" />, color: 'text-green-600', label: 'ممتاز' }
    } else if (efficiency > 50) {
      return { icon: <Minus className="h-4 w-4" />, color: 'text-yellow-600', label: 'جيد' }
    } else {
      return { icon: <TrendingDown className="h-4 w-4" />, color: 'text-red-600', label: 'ضعيف' }
    }
  }

  const getDueDeduction = (item: SalesData) => {
    // الخصم المستحق = السلف + العجز (بدون كسر السعر)
    return item.deficit_amount + item.advances_amount
  }

  const getTotalDeductions = (item: SalesData) => {
    // إجمالي الخصومات = السلف + العجز + كسر السعر (للعرض فقط)
    return item.deficit_amount + item.advances_amount + item.price_breaks_amount
  }

  const getSortIcon = (field: keyof SalesData) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4 opacity-50" />
    }
    return sortDirection === 'asc' 
      ? <ArrowUp className="h-4 w-4" />
      : <ArrowDown className="h-4 w-4" />
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>جدول بيانات المبيعات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">جاري تحميل البيانات...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="rtl-card" dir="rtl">
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              جدول بيانات المبيعات
            </CardTitle>
            <CardDescription>
              عرض {paginatedData.length} من أصل {filteredData.length} سجل مبيعات
            </CardDescription>
          </div>
          
          {/* Search and Controls */}
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="البحث في البيانات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10 w-full sm:w-64"
              />
            </div>
            <Select value={itemsPerPage.toString()} onValueChange={(value) => {
              setItemsPerPage(parseInt(value))
              setCurrentPage(1)
            }}>
              <SelectTrigger className="w-full sm:w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ITEMS_PER_PAGE_OPTIONS.map(option => (
                  <SelectItem key={option} value={option.toString()}>
                    {option} صف
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div
          className="w-full force-scrollbar border rounded-lg"
          style={{
            maxWidth: '100%'
          }}
        >
          <Table className="rtl compact-table" style={{ minWidth: '1400px', width: '1400px', tableLayout: 'fixed' }}>
              <TableHeader>
                <TableRow className="bg-muted/50 hover:bg-muted/50">
                  <TableHead
                    className="text-right cursor-pointer hover:bg-muted/70 transition-colors font-semibold date-column"
                    onClick={() => handleSort('closing_date')}
                  >
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-1 text-xs">
                        <Calendar className="h-3 w-3" />
                        التاريخ
                      </span>
                      {getSortIcon('closing_date')}
                    </div>
                  </TableHead>

                  <TableHead
                    className="text-right cursor-pointer hover:bg-muted/70 transition-colors font-semibold employee-column"
                    onClick={() => handleSort('employee')}
                  >
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-1 text-xs">
                        <User className="h-3 w-3" />
                        الموظف
                      </span>
                      {getSortIcon('employee')}
                    </div>
                  </TableHead>

                  <TableHead className="text-center font-semibold area-column">
                    <span className="flex items-center justify-center gap-1 text-xs">
                      <Users className="h-3 w-3" />
                      المنطقة
                    </span>
                  </TableHead>

                  <TableHead className="text-center font-semibold team-column">
                    <span className="flex items-center justify-center gap-1 text-xs">
                      <Users className="h-3 w-3" />
                      الفريق
                    </span>
                  </TableHead>

                  <TableHead
                    className="text-center cursor-pointer hover:bg-muted/70 transition-colors font-semibold amount-column"
                    onClick={() => handleSort('total_sales_amount')}
                  >
                    <div className="flex items-center justify-center">
                      <span className="flex items-center gap-1 text-xs">
                        <DollarSign className="h-3 w-3" />
                        المبيعات
                      </span>
                      {getSortIcon('total_sales_amount')}
                    </div>
                  </TableHead>

                  <TableHead className="text-center font-semibold w-[120px]">
                    <span className="flex items-center justify-center gap-1 text-xs">
                      <DollarSign className="h-3 w-3" />
                      المسلم
                    </span>
                  </TableHead>

                  <TableHead className="text-center font-semibold w-[140px]">
                    <span className="flex items-center justify-center gap-1 text-xs">
                      <AlertTriangle className="h-3 w-3" />
                      الخصم المستحق
                    </span>
                  </TableHead>

                  <TableHead
                    className="text-center cursor-pointer hover:bg-muted/70 transition-colors font-semibold w-[90px]"
                    onClick={() => handleSort('working_hours')}
                  >
                    <div className="flex items-center justify-center">
                      <span className="flex items-center gap-1 text-xs">
                        <Clock className="h-3 w-3" />
                        الساعات
                      </span>
                      {getSortIcon('working_hours')}
                    </div>
                  </TableHead>

                  <TableHead className="text-center font-semibold w-[80px]">
                    <span className="text-xs">الأداء</span>
                  </TableHead>
                  <TableHead className="text-center font-semibold w-[60px]">
                    <span className="text-xs">الإجراءات</span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Search className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">لا توجد بيانات للعرض</p>
                        {searchQuery && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => setSearchQuery('')}
                          >
                            مسح البحث
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedData.map((item) => {
                    const performance = getPerformanceIndicator(item)
                    const dueDeduction = getDueDeduction(item)
                    const totalDeductions = getTotalDeductions(item)
                    
                    return (
                      <TableRow key={item.id} className="hover:bg-muted/30 transition-colors">
                        <TableCell className="font-medium text-right py-2 px-2">
                          <div className="text-xs">
                            {formatDate(item.closing_date)}
                          </div>
                        </TableCell>

                        <TableCell className="text-right py-2 px-2">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarFallback className="text-xs">
                                <User className="h-3 w-3" />
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col gap-0.5 min-w-0 flex-1">
                              <span className="font-medium text-xs truncate">{item.employee.full_name}</span>
                              <span className="text-xs text-muted-foreground truncate">{item.employee.email}</span>
                            </div>
                          </div>
                        </TableCell>

                        <TableCell className="text-center py-2 px-1">
                          {item.area ? (
                            <Badge variant="outline" className="text-xs px-2 py-1">
                              {item.area.name}
                            </Badge>
                          ) : (
                            <span className="text-xs text-muted-foreground">غير محدد</span>
                          )}
                        </TableCell>

                        <TableCell className="text-center py-2 px-1">
                          {item.team ? (
                            <Badge variant="outline" className="text-xs px-2 py-1">
                              {item.team.name}
                            </Badge>
                          ) : (
                            <span className="text-xs text-muted-foreground">غير محدد</span>
                          )}
                        </TableCell>

                        <TableCell className="text-center font-medium py-2 px-1">
                          <div className="flex flex-col items-center gap-0.5">
                            <div className="text-sm font-bold text-green-700 bg-green-50 px-2 py-1 rounded-md border border-green-200" title={formatCurrency(item.total_sales_amount)}>
                              {formatCurrency(item.total_sales_amount)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatLargeNumber(item.total_sales_amount)}
                            </div>
                          </div>
                        </TableCell>

                        <TableCell className="text-center py-2 px-1">
                          <div className="flex flex-col items-center gap-0.5">
                            <div className="text-sm font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded-md border border-blue-200" title={formatCurrency(item.cash_delivered)}>
                              {formatCurrency(item.cash_delivered)}
                            </div>
                            <div className="text-xs text-muted-foreground bg-gray-100 px-1 py-0.5 rounded">
                              {formatPercentage((item.cash_delivered / item.total_sales_amount) * 100)}
                            </div>
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-center py-2 px-1">
                          {dueDeduction > 0 || item.price_breaks_amount > 0 ? (
                            <div className="flex flex-col gap-1">
                              <div className="text-sm font-bold text-red-700 bg-red-50 px-2 py-1 rounded-md border border-red-200" title={formatCurrency(dueDeduction)}>
                                {formatCurrency(dueDeduction)}
                              </div>
                              <div className="text-xs space-y-0.5">
                                {item.deficit_amount > 0 && (
                                  <div className="bg-red-100 px-1 py-0.5 rounded text-red-700" title={formatCurrency(item.deficit_amount)}>
                                    عجز: {formatCurrency(item.deficit_amount)}
                                  </div>
                                )}
                                {item.advances_amount > 0 && (
                                  <div className="bg-orange-100 px-1 py-0.5 rounded text-orange-700" title={formatCurrency(item.advances_amount)}>
                                    سلف: {formatCurrency(item.advances_amount)}
                                  </div>
                                )}
                                {item.price_breaks_amount > 0 && (
                                  <div className="bg-blue-100 px-1 py-0.5 rounded text-blue-700" title={formatCurrency(item.price_breaks_amount)}>
                                    كسر سعر: {formatCurrency(item.price_breaks_amount)}
                                  </div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-xs bg-gray-100 px-2 py-1 rounded">-</span>
                          )}
                        </TableCell>

                        <TableCell className="text-center py-2 px-1">
                          {item.working_hours ? (
                            <div className="flex flex-col gap-0.5">
                              <div className="text-sm font-semibold text-purple-700 bg-purple-50 px-2 py-1 rounded-md border border-purple-200">
                                {formatTime(item.working_hours)}
                              </div>
                              <div className="text-xs text-muted-foreground bg-gray-100 px-1 py-0.5 rounded">
                                {item.working_hours.toFixed(1)} ساعة
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-xs bg-gray-100 px-2 py-1 rounded">-</span>
                          )}
                        </TableCell>

                        <TableCell className="text-center py-2 px-1">
                          <div className="flex flex-col items-center gap-1">
                            <div className={`${performance.color} p-2 rounded-full bg-opacity-10 ${
                              performance.color.includes('green') ? 'bg-green-100' :
                              performance.color.includes('yellow') ? 'bg-yellow-100' : 'bg-red-100'
                            }`}>
                              {performance.icon}
                            </div>
                            {item.working_hours && (
                              <div className="text-xs font-medium text-gray-700 bg-gray-100 px-2 py-1 rounded-md" title={`${formatCurrency(item.total_sales_amount / item.working_hours)} لكل ساعة`}>
                                {formatCurrency(item.total_sales_amount / item.working_hours)}/ساعة
                              </div>
                            )}
                          </div>
                        </TableCell>

                        <TableCell className="text-center py-2 px-1">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-6 w-6 p-0">
                                <span className="sr-only">فتح القائمة</span>
                                <MoreHorizontal className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="rtl">
                              <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="cursor-pointer">
                                <Eye className="mr-2 h-4 w-4" />
                                عرض التفاصيل
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-muted-foreground">
              عرض {startIndex + 1} إلى {Math.min(endIndex, filteredData.length)} من أصل {filteredData.length} نتيجة
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              
              <div className="flex items-center gap-1">
                <span className="text-sm">صفحة</span>
                <span className="text-sm font-medium">{currentPage}</span>
                <span className="text-sm">من</span>
                <span className="text-sm font-medium">{totalPages}</span>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
