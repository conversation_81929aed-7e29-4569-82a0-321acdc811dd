'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  DollarSign, 
  Plus, 
  User, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  Edit
} from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { toast } from '@/hooks/use-toast'
import type { Database } from '@/lib/supabase'

type EmployeeAdvance = Database['public']['Tables']['employee_advances']['Row']
type Profile = Database['public']['Tables']['profiles']['Row']

interface EmployeeAdvancesManagerProps {
  userRole: string
  userAreaId?: string
  userTeamId?: string
  onAdvanceUpdate: () => void
}

interface AdvanceWithEmployee extends EmployeeAdvance {
  employee: Profile
}

export function EmployeeAdvancesManager({ 
  userRole, 
  userAreaId, 
  userTeamId, 
  onAdvanceUpdate 
}: EmployeeAdvancesManagerProps) {
  const [advances, setAdvances] = useState<AdvanceWithEmployee[]>([])
  const [employees, setEmployees] = useState<Profile[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingAdvance, setEditingAdvance] = useState<AdvanceWithEmployee | null>(null)

  // Form state
  const [selectedEmployee, setSelectedEmployee] = useState('')
  const [amount, setAmount] = useState('')
  const [reason, setReason] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const supabase = createClient()

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Fetch employees based on role
      let employeesQuery = supabase
        .from('profiles')
        .select('id, full_name, email, role, area_id, team_id')
        .order('full_name')

      if (userRole === 'team_manager' && userTeamId) {
        employeesQuery = employeesQuery.eq('team_id', userTeamId)
      } else if (userRole === 'area_manager' && userAreaId) {
        employeesQuery = employeesQuery.eq('area_id', userAreaId)
      }

      const { data: employeesData, error: employeesError } = await employeesQuery

      if (employeesError) throw employeesError
      setEmployees(employeesData || [])

      // Fetch advances
      let advancesQuery = supabase
        .from('employee_advances')
        .select(`
          *,
          employee:profiles!employee_advances_employee_id_fkey(
            id, full_name, email, role, area_id, team_id
          )
        `)
        .order('request_date', { ascending: false })

      // Apply role-based filtering for advances
      if (userRole === 'team_manager' && userTeamId) {
        const teamMemberIds = employeesData?.map(emp => emp.id) || []
        if (teamMemberIds.length > 0) {
          advancesQuery = advancesQuery.in('employee_id', teamMemberIds)
        }
      } else if (userRole === 'area_manager' && userAreaId) {
        const areaMemberIds = employeesData?.map(emp => emp.id) || []
        if (areaMemberIds.length > 0) {
          advancesQuery = advancesQuery.in('employee_id', areaMemberIds)
        }
      }

      const { data: advancesData, error: advancesError } = await advancesQuery

      if (advancesError) throw advancesError
      setAdvances(advancesData || [])

    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedEmployee || !amount || parseFloat(amount) <= 0) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    try {
      setSubmitting(true)

      const response = await fetch('/api/salary/advances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employee_id: selectedEmployee,
          amount: parseFloat(amount),
          reason: reason || null
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في إنشاء السلفة')
      }

      toast.success('تم إنشاء السلفة بنجاح')
      resetForm()
      setDialogOpen(false)
      fetchData()
      onAdvanceUpdate()

    } catch (error) {
      console.error('Error creating advance:', error)
      toast.error(error instanceof Error ? error.message : 'حدث خطأ في إنشاء السلفة')
    } finally {
      setSubmitting(false)
    }
  }

  const updateAdvanceStatus = async (advanceId: string, status: string) => {
    try {
      const response = await fetch('/api/salary/advances', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          advance_id: advanceId,
          status
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في تحديث حالة السلفة')
      }

      toast.success('تم تحديث حالة السلفة بنجاح')
      fetchData()
      onAdvanceUpdate()

    } catch (error) {
      console.error('Error updating advance status:', error)
      toast.error(error instanceof Error ? error.message : 'حدث خطأ في تحديث حالة السلفة')
    }
  }

  const resetForm = () => {
    setSelectedEmployee('')
    setAmount('')
    setReason('')
    setEditingAdvance(null)
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ر.س`
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="gap-1"><Clock className="h-3 w-3" />في الانتظار</Badge>
      case 'approved':
        return <Badge variant="default" className="gap-1"><CheckCircle className="h-3 w-3" />موافق عليها</Badge>
      case 'deducted':
        return <Badge variant="outline" className="gap-1"><DollarSign className="h-3 w-3" />تم الخصم</Badge>
      case 'cancelled':
        return <Badge variant="destructive" className="gap-1"><XCircle className="h-3 w-3" />ملغية</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const canApprove = ['system_admin', 'area_manager'].includes(userRole)
  const canCreate = ['system_admin', 'area_manager', 'team_manager'].includes(userRole)

  if (loading) {
    return (
      <div className="space-y-6" dir="rtl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات السلف...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold">إدارة السلف</h3>
          <p className="text-muted-foreground">
            إدارة طلبات السلف والموافقة عليها
          </p>
        </div>
        {canCreate && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                سلفة جديدة
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md" dir="rtl">
              <DialogHeader>
                <DialogTitle>إنشاء سلفة جديدة</DialogTitle>
                <DialogDescription>
                  إنشاء طلب سلفة جديد للموظف
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label>الموظف</Label>
                  <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر موظف" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map(employee => (
                        <SelectItem key={employee.id} value={employee.id}>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            {employee.full_name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>المبلغ</Label>
                  <div className="relative">
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      className="pr-12"
                      placeholder="0.00"
                      required
                    />
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                      ر.س
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>السبب (اختياري)</Label>
                  <Textarea
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    placeholder="سبب طلب السلفة..."
                    rows={3}
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      resetForm()
                      setDialogOpen(false)
                    }}
                  >
                    إلغاء
                  </Button>
                  <Button type="submit" disabled={submitting}>
                    {submitting ? 'جاري الإنشاء...' : 'إنشاء السلفة'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Advances Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            قائمة السلف
          </CardTitle>
          <CardDescription>
            جميع طلبات السلف وحالاتها
          </CardDescription>
        </CardHeader>
        <CardContent>
          {advances.length === 0 ? (
            <div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">لا توجد طلبات سلف</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الموظف</TableHead>
                    <TableHead>المبلغ</TableHead>
                    <TableHead>تاريخ الطلب</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>السبب</TableHead>
                    {canApprove && <TableHead>الإجراءات</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {advances.map((advance) => (
                    <TableRow key={advance.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{advance.employee.full_name}</div>
                            <div className="text-sm text-muted-foreground">{advance.employee.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(advance.amount)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {new Date(advance.request_date).toLocaleDateString('ar-SA')}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(advance.status)}
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs truncate">
                          {advance.reason || 'لا يوجد سبب محدد'}
                        </div>
                      </TableCell>
                      {canApprove && (
                        <TableCell>
                          <div className="flex gap-1">
                            {advance.status === 'pending' && (
                              <>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => updateAdvanceStatus(advance.id, 'approved')}
                                  className="gap-1"
                                >
                                  <CheckCircle className="h-3 w-3" />
                                  موافقة
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => updateAdvanceStatus(advance.id, 'cancelled')}
                                  className="gap-1"
                                >
                                  <XCircle className="h-3 w-3" />
                                  رفض
                                </Button>
                              </>
                            )}
                            {advance.status === 'approved' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => updateAdvanceStatus(advance.id, 'deducted')}
                                className="gap-1"
                              >
                                <DollarSign className="h-3 w-3" />
                                تم الخصم
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
