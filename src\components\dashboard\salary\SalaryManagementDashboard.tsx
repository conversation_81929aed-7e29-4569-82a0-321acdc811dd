'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { 
  Calculator, 
  Settings, 
  Users, 
  DollarSign, 
  Calendar,
  TrendingUp,
  Download,
  Plus
} from 'lucide-react'
import { SalaryCalculationForm } from './SalaryCalculationForm'
import { SalarySettingsForm } from './SalarySettingsForm'
import { EmployeeAdvancesManager } from './EmployeeAdvancesManager'
import { SalaryRecordsTable } from './SalaryRecordsTable'
import { SalaryMetricsCards } from './SalaryMetricsCards'
import { createClient } from '@/lib/supabase/client'
import { toast } from '@/hooks/use-toast'
import type { Database } from '@/lib/supabase'

type SalarySettings = Database['public']['Tables']['salary_settings']['Row']
type SalaryCalculation = Database['public']['Tables']['monthly_salary_calculations']['Row']

interface SalaryManagementDashboardProps {
  userRole: string
  userId: string
  userAreaId?: string
  userTeamId?: string
}

export function SalaryManagementDashboard({ 
  userRole, 
  userId, 
  userAreaId, 
  userTeamId 
}: SalaryManagementDashboardProps) {
  const [currentSettings, setCurrentSettings] = useState<SalarySettings | null>(null)
  const [salaryRecords, setSalaryRecords] = useState<SalaryCalculation[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  const supabase = createClient()

  useEffect(() => {
    fetchInitialData()
  }, [])

  const fetchInitialData = async () => {
    try {
      setLoading(true)

      // Fetch current salary settings
      const { data: settings, error: settingsError } = await supabase
        .from('salary_settings')
        .select('*')
        .eq('is_active', true)
        .order('effective_from', { ascending: false })
        .limit(1)
        .single()

      if (settingsError && settingsError.code !== 'PGRST116') {
        console.error('Error fetching salary settings:', settingsError)
      } else {
        setCurrentSettings(settings)
      }

      // Fetch recent salary records based on user role
      let recordsQuery = supabase
        .from('monthly_salary_calculations')
        .select(`
          *,
          employee:profiles!monthly_salary_calculations_employee_id_fkey(
            id, full_name, email, role, area_id, team_id
          ),
          deductions:salary_deductions(*)
        `)
        .order('calculation_date', { ascending: false })
        .limit(50)

      // Apply role-based filtering
      if (userRole === 'sales_employee') {
        recordsQuery = recordsQuery.eq('employee_id', userId)
      } else if (userRole === 'team_manager' && userTeamId) {
        // Get team members
        const { data: teamMembers } = await supabase
          .from('profiles')
          .select('id')
          .eq('team_id', userTeamId)

        if (teamMembers && teamMembers.length > 0) {
          const memberIds = teamMembers.map(member => member.id)
          recordsQuery = recordsQuery.in('employee_id', memberIds)
        }
      } else if (userRole === 'area_manager' && userAreaId) {
        // Get area members
        const { data: areaMembers } = await supabase
          .from('profiles')
          .select('id')
          .eq('area_id', userAreaId)

        if (areaMembers && areaMembers.length > 0) {
          const memberIds = areaMembers.map(member => member.id)
          recordsQuery = recordsQuery.in('employee_id', memberIds)
        }
      }

      const { data: records, error: recordsError } = await recordsQuery

      if (recordsError) {
        console.error('Error fetching salary records:', recordsError)
        toast.error('حدث خطأ في تحميل سجلات الرواتب')
      } else {
        setSalaryRecords(records || [])
      }

    } catch (error) {
      console.error('Error fetching initial data:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const refreshData = () => {
    fetchInitialData()
  }

  const canManageSettings = userRole === 'system_admin'
  const canCalculateSalaries = ['system_admin', 'area_manager', 'team_manager'].includes(userRole)
  const canViewAdvances = ['system_admin', 'area_manager', 'team_manager'].includes(userRole)

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ر.س`
  }

  if (loading) {
    return (
      <div className="space-y-6" dir="rtl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات الرواتب...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">إدارة الرواتب</h2>
          <p className="text-muted-foreground">
            حساب وإدارة رواتب الموظفين والخصومات
          </p>
        </div>
        <div className="flex gap-2">
          {canCalculateSalaries && (
            <Button 
              onClick={() => setActiveTab('calculate')} 
              className="gap-2"
            >
              <Calculator className="h-4 w-4" />
              حساب راتب
            </Button>
          )}
          {canManageSettings && (
            <Button 
              onClick={() => setActiveTab('settings')} 
              variant="outline" 
              className="gap-2"
            >
              <Settings className="h-4 w-4" />
              الإعدادات
            </Button>
          )}
        </div>
      </div>

      {/* Current Settings Display */}
      {currentSettings && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              الإعدادات الحالية للرواتب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(currentSettings.base_salary)}
                </div>
                <div className="text-sm text-muted-foreground">الراتب الأساسي</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {currentSettings.working_hours_per_day} ساعة
                </div>
                <div className="text-sm text-muted-foreground">ساعات العمل اليومية</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {currentSettings.currency}
                </div>
                <div className="text-sm text-muted-foreground">العملة</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metrics Cards */}
      <SalaryMetricsCards 
        data={salaryRecords} 
        loading={false}
        userRole={userRole}
      />

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="gap-2">
            <TrendingUp className="h-4 w-4" />
            نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="records" className="gap-2">
            <Users className="h-4 w-4" />
            سجلات الرواتب
          </TabsTrigger>
          {canCalculateSalaries && (
            <TabsTrigger value="calculate" className="gap-2">
              <Calculator className="h-4 w-4" />
              حساب راتب
            </TabsTrigger>
          )}
          {canViewAdvances && (
            <TabsTrigger value="advances" className="gap-2">
              <DollarSign className="h-4 w-4" />
              السلف
            </TabsTrigger>
          )}
          {canManageSettings && (
            <TabsTrigger value="settings" className="gap-2">
              <Settings className="h-4 w-4" />
              الإعدادات
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>نظرة عامة على الرواتب</CardTitle>
                <CardDescription>
                  ملخص حالة الرواتب والإحصائيات
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    سيتم إضافة الرسوم البيانية والإحصائيات التفصيلية قريباً...
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="records">
          <SalaryRecordsTable 
            data={salaryRecords}
            loading={false}
            onRefresh={refreshData}
            userRole={userRole}
          />
        </TabsContent>

        {canCalculateSalaries && (
          <TabsContent value="calculate">
            <SalaryCalculationForm 
              currentSettings={currentSettings}
              onCalculationComplete={refreshData}
              userRole={userRole}
              userAreaId={userAreaId}
              userTeamId={userTeamId}
            />
          </TabsContent>
        )}

        {canViewAdvances && (
          <TabsContent value="advances">
            <EmployeeAdvancesManager 
              userRole={userRole}
              userAreaId={userAreaId}
              userTeamId={userTeamId}
              onAdvanceUpdate={refreshData}
            />
          </TabsContent>
        )}

        {canManageSettings && (
          <TabsContent value="settings">
            <SalarySettingsForm 
              currentSettings={currentSettings}
              onSettingsUpdate={(newSettings) => {
                setCurrentSettings(newSettings)
                refreshData()
              }}
            />
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
