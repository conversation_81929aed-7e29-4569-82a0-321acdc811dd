'use client'

import * as React from 'react'
import { CalendarIcon, X } from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface SimpleDatePickerProps {
  date: {
    from: Date | undefined
    to: Date | undefined
  }
  onDateChange: (date: { from: Date | undefined; to: Date | undefined }) => void
  className?: string
  placeholder?: string
}

const getMonthName = (month: number): string => {
  const months = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ]
  return months[month - 1] || ''
}

export function SimpleDatePicker({
  date,
  onDateChange,
  className,
  placeholder = 'اختر نطاق التاريخ'
}: SimpleDatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [fromMonth, setFromMonth] = React.useState<number>(date.from?.getMonth() + 1 || new Date().getMonth() + 1)
  const [fromYear, setFromYear] = React.useState<number>(date.from?.getFullYear() || new Date().getFullYear())
  const [fromDay, setFromDay] = React.useState<number>(date.from?.getDate() || 1)
  const [toMonth, setToMonth] = React.useState<number>(date.to?.getMonth() + 1 || new Date().getMonth() + 1)
  const [toYear, setToYear] = React.useState<number>(date.to?.getFullYear() || new Date().getFullYear())
  const [toDay, setToDay] = React.useState<number>(date.to?.getDate() || new Date().getDate())

  const currentYear = new Date().getFullYear()
  const availableYears = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i)

  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month, 0).getDate()
  }

  const handleApply = () => {
    const fromDate = new Date(fromYear, fromMonth - 1, fromDay)
    const toDate = new Date(toYear, toMonth - 1, toDay)
    
    // Ensure from date is not after to date
    if (fromDate > toDate) {
      onDateChange({ from: toDate, to: fromDate })
    } else {
      onDateChange({ from: fromDate, to: toDate })
    }
    setIsOpen(false)
  }

  const handleClear = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation()
    }
    onDateChange({ from: undefined, to: undefined })
    setIsOpen(false)
  }

  const handleQuickSelect = (days: number) => {
    const today = new Date()
    const fromDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - days + 1)
    onDateChange({ from: fromDate, to: today })
    setIsOpen(false)
  }

  React.useEffect(() => {
    if (date.from) {
      setFromMonth(date.from.getMonth() + 1)
      setFromYear(date.from.getFullYear())
      setFromDay(date.from.getDate())
    }
    if (date.to) {
      setToMonth(date.to.getMonth() + 1)
      setToYear(date.to.getFullYear())
      setToDay(date.to.getDate())
    }
  }, [date])

  const displayText = date?.from && date?.to
    ? `${format(date.from, 'dd/MM/yyyy', { locale: ar })} - ${format(date.to, 'dd/MM/yyyy', { locale: ar })}`
    : date?.from
    ? format(date.from, 'dd/MM/yyyy', { locale: ar })
    : placeholder

  return (
    <div className={cn('relative', className)} dir="rtl">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-right font-normal h-10 px-3 py-2 pr-10',
              !date.from && !date.to && 'text-muted-foreground'
            )}
          >
            <CalendarIcon className="h-4 w-4" />
            <span className="flex-1 text-right truncate">
              {displayText}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96 p-0" align="start" dir="rtl">
          <div className="p-4 border-b">
            <h4 className="font-medium text-sm text-right mb-3">اختر نطاق التاريخ</h4>
            
            {/* Quick Select Buttons */}
            <div className="grid grid-cols-4 gap-2 mb-4">
              <Button variant="outline" size="sm" onClick={() => handleQuickSelect(7)}>
                آخر 7 أيام
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleQuickSelect(30)}>
                آخر 30 يوم
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleQuickSelect(90)}>
                آخر 3 شهور
              </Button>
              <Button variant="outline" size="sm" onClick={() => {
                const today = new Date()
                const firstDay = new Date(today.getFullYear(), today.getMonth(), 1)
                onDateChange({ from: firstDay, to: today })
                setIsOpen(false)
              }}>
                هذا الشهر
              </Button>
            </div>
          </div>

          <div className="p-4 space-y-4">
            {/* From Date */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">من تاريخ</Label>
              <div className="grid grid-cols-3 gap-2">
                <Select value={fromDay.toString()} onValueChange={(value) => setFromDay(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: getDaysInMonth(fromMonth, fromYear) }, (_, i) => i + 1).map(day => (
                      <SelectItem key={day} value={day.toString()}>
                        {day}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={fromMonth.toString()} onValueChange={(value) => setFromMonth(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                      <SelectItem key={month} value={month.toString()}>
                        {getMonthName(month)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={fromYear.toString()} onValueChange={(value) => setFromYear(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableYears.map(year => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* To Date */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">إلى تاريخ</Label>
              <div className="grid grid-cols-3 gap-2">
                <Select value={toDay.toString()} onValueChange={(value) => setToDay(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: getDaysInMonth(toMonth, toYear) }, (_, i) => i + 1).map(day => (
                      <SelectItem key={day} value={day.toString()}>
                        {day}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={toMonth.toString()} onValueChange={(value) => setToMonth(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                      <SelectItem key={month} value={month.toString()}>
                        {getMonthName(month)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={toYear.toString()} onValueChange={(value) => setToYear(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableYears.map(year => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="p-4 border-t flex justify-between">
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setIsOpen(false)}>
                إلغاء
              </Button>
              <Button size="sm" onClick={handleApply}>
                تطبيق
              </Button>
            </div>
            <Button variant="ghost" size="sm" onClick={() => handleClear()}>
              مسح
            </Button>
          </div>
        </PopoverContent>
      </Popover>
      
      {(date?.from || date?.to) && (
        <button
          type="button"
          className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none cursor-pointer"
          onClick={handleClear}
        >
          <X className="h-3 w-3" />
          <span className="sr-only">مسح</span>
        </button>
      )}
    </div>
  )
}
