@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-ibm-plex-arabic);
  --font-arabic: var(--font-ibm-plex-arabic);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* White background with dark blue accents */
  --background: oklch(1 0 0); /* Pure white */
  --foreground: oklch(0.2 0.05 240); /* Dark blue text */
  --card: oklch(1 0 0); /* White cards */
  --card-foreground: oklch(0.2 0.05 240); /* Dark blue text on cards */
  --popover: oklch(1 0 0); /* White popovers */
  --popover-foreground: oklch(0.2 0.05 240); /* Dark blue text on popovers */
  --primary: oklch(0.25 0.08 240); /* Dark blue primary */
  --primary-foreground: oklch(1 0 0); /* White text on primary */
  --secondary: oklch(0.95 0.01 240); /* Very light blue secondary */
  --secondary-foreground: oklch(0.2 0.05 240); /* Dark blue text on secondary */
  --muted: oklch(0.96 0.005 240); /* Very light blue muted */
  --muted-foreground: oklch(0.45 0.03 240); /* Medium blue muted text */
  --accent: oklch(0.3 0.06 240); /* Slightly lighter dark blue accent */
  --accent-foreground: oklch(1 0 0); /* White text on accent */
  --destructive: oklch(0.577 0.245 27.325); /* Keep red for destructive */
  --border: oklch(0.9 0.01 240); /* Light blue borders */
  --input: oklch(0.98 0.005 240); /* Very light blue inputs */
  --ring: oklch(0.3 0.06 240); /* Dark blue focus ring */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: #002443;
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: #002443;
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-ibm-plex-arabic), sans-serif;
    direction: rtl;
    overflow-x: hidden;
  }

  /* Arabic font utilities */
  .font-arabic {
    font-family: var(--font-ibm-plex-arabic), sans-serif;
  }

  /* RTL utilities */
  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }
}

/* Global pointer cursor for all clickable elements */
button,
[role="button"],
[type="button"],
[type="submit"],
[type="reset"],
input[type="checkbox"],
input[type="radio"],
select,
a,
[href],
[onclick],
[tabindex]:not([tabindex="-1"]),
.cursor-pointer,
[data-clickable],
[data-radix-collection-item],
[data-radix-dropdown-menu-item],
[data-radix-select-item],
[data-radix-menubar-item],
[data-radix-context-menu-item],
[data-radix-navigation-menu-link],
[data-radix-accordion-trigger],
[data-radix-collapsible-trigger],
[data-radix-dialog-trigger],
[data-radix-popover-trigger],
[data-radix-tooltip-trigger],
[data-radix-hover-card-trigger],
[data-radix-alert-dialog-trigger],
[data-radix-sheet-trigger],
[data-radix-tabs-trigger],
[data-radix-toggle],
[data-radix-switch-thumb],
[data-radix-slider-thumb],
[data-radix-scroll-area-thumb],
[data-radix-menubar-trigger],
[data-radix-dropdown-menu-trigger],
[data-radix-context-menu-trigger],
[data-radix-select-trigger],
[data-radix-combobox-trigger],
[data-radix-date-picker-trigger],
[data-radix-calendar-day],
[data-radix-calendar-grid-cell],
[data-radix-pagination-link],
[data-radix-breadcrumb-link],
[data-radix-avatar],
[data-radix-badge],
.clickable,
.interactive,
.selectable {
  cursor: pointer !important;
}

/* Ensure disabled elements don't show pointer */
button:disabled,
[role="button"]:disabled,
[type="button"]:disabled,
[type="submit"]:disabled,
[type="reset"]:disabled,
input:disabled,
select:disabled,
[aria-disabled="true"],
[data-disabled="true"],
[data-state="disabled"],
.disabled {
  cursor: not-allowed !important;
}

/* Loading states should show default cursor */
[data-loading="true"],
.loading {
  cursor: wait !important;
}

/* Text selection areas should show text cursor */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="number"],
textarea,
[contenteditable="true"],
[role="textbox"] {
  cursor: text !important;
}

/* Resize handles */
[data-radix-scroll-area-scrollbar],
[data-radix-resizable-handle] {
  cursor: grab !important;
}

[data-radix-scroll-area-scrollbar]:active,
[data-radix-resizable-handle]:active {
  cursor: grabbing !important;
}

/* Custom component cursors */
.table-row-clickable,
.card-clickable,
.list-item-clickable,
.menu-item,
.dropdown-item,
.sidebar-item,
.nav-item,
.tab-item,
.accordion-item,
.collapsible-item,
.dialog-trigger,
.sheet-trigger,
.popover-trigger,
.tooltip-trigger,
.hover-card-trigger,
.alert-dialog-trigger,
.breadcrumb-item,
.pagination-item,
.calendar-day,
.date-picker-day,
.avatar-clickable,
.badge-clickable,
.chip-clickable,
.tag-clickable {
  cursor: pointer !important;
}

/* Table specific cursors */
table tr[onclick],
table tr[data-clickable],
table tr.clickable,
table tbody tr:hover,
.table-hover tr:hover {
  cursor: pointer !important;
}

/* Card specific cursors */
.card[onclick],
.card[data-clickable],
.card.clickable,
.card-hover:hover {
  cursor: pointer !important;
}

/* Icon buttons and icon-only elements */
.icon-button,
.icon-clickable,
[data-icon-button],
svg[onclick],
svg[data-clickable] {
  cursor: pointer !important;
}

/* Form elements that should show pointer */
label[for],
.form-label[for],
.checkbox-label,
.radio-label,
.switch-label {
  cursor: pointer !important;
}

/* Additional interactive elements */
.hover\:bg-muted\/50:hover,
.hover\:bg-accent:hover,
.hover\:bg-secondary:hover,
.hover\:opacity-80:hover,
.hover\:opacity-90:hover,
.hover\:opacity-100:hover,
.hover\:scale-105:hover,
.hover\:shadow-md:hover,
.hover\:shadow-lg:hover,
.transition-all,
.transition-colors,
.transition-opacity,
.transition-transform,
.transition-shadow {
  cursor: pointer !important;
}

/* Specific component cursors */
[data-slot="breadcrumb-link"],
[data-slot="sidebar-menu-button"],
[data-slot="sidebar-menu-action"],
[data-slot="dropdown-menu-item"],
[data-slot="select-item"],
[data-slot="dialog-trigger"],
[data-slot="sheet-trigger"],
[data-slot="alert-dialog-trigger"],
[data-slot="tooltip-trigger"],
[data-slot="popover-trigger"],
[data-slot="collapsible-trigger"],
[data-slot="accordion-trigger"] {
  cursor: pointer !important;
}

/* Avatar and profile elements */
.avatar,
.profile-avatar,
.user-avatar {
  cursor: pointer !important;
}

/* Navigation and menu items */
.nav-link,
.menu-link,
.sidebar-link,
.breadcrumb-link,
.pagination-link {
  cursor: pointer !important;
}

/* Enhanced smooth transitions */
@layer base {
  * {
    /* Removed transform from global transitions to prevent interference with Radix UI animations */
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  /* Exclude Radix UI dropdown/popover elements from global transitions */
  [data-radix-dropdown-menu-content],
  [data-radix-select-content],
  [data-radix-popover-content],
  [data-radix-tooltip-content],
  [data-radix-context-menu-content],
  [data-radix-menubar-content],
  [data-radix-navigation-menu-content],
  [data-radix-hover-card-content],
  [data-radix-alert-dialog-content],
  [data-radix-dialog-content],
  [data-radix-sheet-content] {
    transition: none !important;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced focus states */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    transition: outline-offset 150ms ease;
  }

  /* Smooth hover states for interactive elements - with specific transform transitions */
  button, a, [role="button"] {
    transition: color 200ms ease, background-color 200ms ease, border-color 200ms ease, opacity 200ms ease, box-shadow 200ms ease, transform 200ms ease;
  }

  button:hover:not(:disabled), a:hover, [role="button"]:hover:not([aria-disabled="true"]) {
    transform: translateY(-1px);
  }

  /* Ensure Radix UI triggers don't get the hover transform */
  [data-radix-dropdown-menu-trigger]:hover,
  [data-radix-select-trigger]:hover,
  [data-radix-popover-trigger]:hover,
  [data-radix-context-menu-trigger]:hover,
  [data-radix-menubar-trigger]:hover {
    transform: none !important;
  }

  /* Loading animation improvements */
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes bounce {
    0%, 100% {
      transform: translateY(-25%);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
      transform: translateY(0);
      animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
  }

  /* Skeleton loading animation */
  @keyframes skeleton {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .animate-skeleton {
    background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted-foreground) / 0.1) 50%, hsl(var(--muted)) 75%);
    background-size: 200px 100%;
    animation: skeleton 1.5s infinite linear;
  }

  /* Smooth sidebar transitions */
  [data-slot="sidebar"] {
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Page transition animations */
  .page-enter {
    opacity: 0;
    transform: translateY(10px);
  }

  .page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms ease, transform 300ms ease;
  }

  .page-exit {
    opacity: 1;
    transform: translateY(0);
  }

  .page-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 200ms ease, transform 200ms ease;
  }

  /* Custom Scrollbar Styles - Enhanced Design */
  /* For Webkit browsers (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.2);
    border-radius: 6px;
    margin: 4px;
    border: 1px solid hsl(var(--border) / 0.3);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.8) 0%,
      hsl(var(--primary) / 0.6) 50%,
      hsl(var(--primary) / 0.7) 100%);
    border-radius: 6px;
    border: 1px solid hsl(var(--primary) / 0.3);
    transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px hsl(var(--primary) / 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.95) 0%,
      hsl(var(--primary) / 0.8) 50%,
      hsl(var(--primary) / 0.85) 100%);
    border-color: hsl(var(--primary) / 0.5);
    box-shadow: 0 4px 8px hsl(var(--primary) / 0.2);
    transform: scaleX(1.1);
  }

  ::-webkit-scrollbar-thumb:active {
    background: hsl(var(--primary));
    border-color: hsl(var(--primary) / 0.7);
    box-shadow: 0 2px 4px hsl(var(--primary) / 0.3);
  }

  ::-webkit-scrollbar-corner {
    background: hsl(var(--muted) / 0.2);
    border-radius: 6px;
  }

  /* For Firefox */
  html {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary) / 0.7) hsl(var(--muted) / 0.2);
  }

  /* RTL Scrollbar positioning for the main content area */
  body {
    overflow-x: hidden;
    direction: rtl;
  }

  /* Custom scrollbar container for RTL layout */
  .rtl-scrollbar {
    direction: rtl;
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* RTL-specific scrollbar positioning */
  html[dir="rtl"] body,
  html[dir="rtl"] .rtl-scrollbar {
    direction: rtl;
  }

  /* Enhanced RTL scrollbar container */
  .rtl-scrollbar-container {
    position: relative;
    direction: rtl;
    overflow: hidden;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .rtl-scrollbar-content {
    direction: rtl;
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    padding-left: 12px; /* Space for custom scrollbar */
    margin-left: -2px; /* Adjust for scrollbar positioning */
  }

  /* Force scrollbar to left side using CSS transforms for webkit */
  html[dir="rtl"] .rtl-scrollbar-content::-webkit-scrollbar {
    position: absolute;
    left: 0;
    right: auto;
  }

  /* Alternative approach: Use CSS to simulate left-side scrollbar */
  .custom-rtl-scrollbar {
    position: relative;
    overflow: hidden;
  }

  .custom-rtl-scrollbar::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 12px;
    height: 100%;
    background: hsl(var(--muted) / 0.1);
    border-radius: 6px;
    z-index: 1;
    pointer-events: none;
  }

  .rtl-scrollbar::-webkit-scrollbar {
    width: 10px;
  }

  .rtl-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.15);
    border-radius: 6px;
    margin: 6px 0;
    border: 1px solid hsl(var(--border) / 0.2);
  }

  .rtl-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.85) 0%,
      hsl(var(--primary) / 0.65) 50%,
      hsl(var(--primary) / 0.75) 100%);
    border-radius: 6px;
    border: 1px solid hsl(var(--primary) / 0.4);
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 2px 4px hsl(var(--primary) / 0.1),
      inset 0 1px 0 hsl(var(--primary) / 0.2);
  }

  .rtl-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.95) 0%,
      hsl(var(--primary) / 0.8) 50%,
      hsl(var(--primary) / 0.9) 100%);
    border-color: hsl(var(--primary) / 0.6);
    transform: scaleX(1.15);
    box-shadow:
      0 4px 8px hsl(var(--primary) / 0.2),
      inset 0 1px 0 hsl(var(--primary) / 0.3);
  }

  .rtl-scrollbar::-webkit-scrollbar-thumb:active {
    background: hsl(var(--primary));
    border-color: hsl(var(--primary) / 0.8);
    transform: scaleX(1.05);
    box-shadow:
      0 2px 4px hsl(var(--primary) / 0.3),
      inset 0 1px 0 hsl(var(--primary) / 0.4);
  }

  /* Dark mode scrollbar adjustments */
  .dark ::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.2);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.7);
    border: 1px solid hsl(var(--border));
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.9);
  }

  .dark .rtl-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, hsl(var(--primary) / 0.8), hsl(var(--primary) / 0.6));
  }

  .dark .rtl-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  }

  /* Smooth scrolling behavior */
  .rtl-scrollbar {
    scroll-behavior: smooth;
  }

  /* Hide scrollbar for specific elements when needed */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Custom scrollbar for sidebar content */
  [data-radix-scroll-area-viewport] {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--sidebar-primary) / 0.6) transparent;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar {
    width: 6px;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
    background: transparent;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
    background: hsl(var(--sidebar-primary) / 0.4);
    border-radius: 3px;
    transition: background-color 200ms ease;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--sidebar-primary) / 0.6);
  }

  /* RTL Scrollbar enabled body styles */
  body.rtl-scrollbar-enabled {
    overflow-x: hidden;
  }

  body.rtl-scrollbar-enabled .rtl-scrollbar-container {
    position: relative;
    direction: rtl;
  }

  body.rtl-scrollbar-enabled .rtl-scrollbar-content {
    direction: rtl;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary) / 0.7) hsl(var(--muted) / 0.2);
  }

  /* Enhanced scrollbar for RTL content */
  .rtl-scrollbar-content::-webkit-scrollbar {
    width: 12px;
  }

  .rtl-scrollbar-content::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.1);
    border-radius: 8px;
    margin: 8px 0;
    border: 1px solid hsl(var(--border) / 0.1);
  }

  .rtl-scrollbar-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.9) 0%,
      hsl(var(--primary) / 0.7) 50%,
      hsl(var(--primary) / 0.8) 100%);
    border-radius: 8px;
    border: 2px solid hsl(var(--background));
    box-shadow:
      0 2px 6px hsl(var(--primary) / 0.15),
      inset 0 1px 0 hsl(var(--primary) / 0.3),
      inset 0 -1px 0 hsl(var(--primary) / 0.1);
    transition: all 350ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .rtl-scrollbar-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      hsl(var(--primary)) 0%,
      hsl(var(--primary) / 0.85) 50%,
      hsl(var(--primary) / 0.95) 100%);
    border-color: hsl(var(--background));
    transform: scaleX(1.2);
    box-shadow:
      0 4px 12px hsl(var(--primary) / 0.25),
      inset 0 1px 0 hsl(var(--primary) / 0.4),
      inset 0 -1px 0 hsl(var(--primary) / 0.2);
  }

  .rtl-scrollbar-content::-webkit-scrollbar-thumb:active {
    background: hsl(var(--primary));
    transform: scaleX(1.1);
    box-shadow:
      0 2px 6px hsl(var(--primary) / 0.3),
      inset 0 1px 0 hsl(var(--primary) / 0.5);
  }

  /* Dark mode enhancements for RTL scrollbar */
  .dark .rtl-scrollbar-content::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.05);
    border-color: hsl(var(--border) / 0.05);
  }

  .dark .rtl-scrollbar-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.8) 0%,
      hsl(var(--primary) / 0.6) 50%,
      hsl(var(--primary) / 0.7) 100%);
    border-color: hsl(var(--background));
  }

  .dark .rtl-scrollbar-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.95) 0%,
      hsl(var(--primary) / 0.8) 50%,
      hsl(var(--primary) / 0.9) 100%);
  }
}

/* Modern Loading Animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
}

/* Enhanced bounce animation for dots */
@keyframes bounce-smooth {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.animate-bounce-smooth {
  animation: bounce-smooth 1.4s infinite ease-in-out;
}

/* Enhanced Sidebar Menu Button Hover and Active Effects */
[data-sidebar="menu-button"] {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

[data-sidebar="menu-button"]:hover {
  background-color: #002443 !important;
  color: white !important;
}

[data-sidebar="menu-button"]:hover svg {
  color: white !important;
}

[data-sidebar="menu-button"][data-active="true"] {
  background-color: #002443 !important;
  color: white !important;
  font-weight: 600;
  border-right: 3px solid hsl(var(--sidebar-primary));
}

[data-sidebar="menu-button"][data-active="true"] svg {
  color: white !important;
}

/* Enhanced Sidebar Sub Menu Button Effects */
[data-sidebar="menu-sub-button"] {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

[data-sidebar="menu-sub-button"]:hover {
  background-color: rgba(0, 36, 67, 0.7) !important;
  color: white !important;
}

[data-sidebar="menu-sub-button"][data-active="true"] {
  background-color: rgba(0, 36, 67, 0.8) !important;
  color: white !important;
  font-weight: 600;
  border-right: 3px solid hsl(var(--sidebar-primary));
}

/* Collapsible trigger enhanced effects */
[data-sidebar="menu-button"] .lucide-chevron-left {
  transition: all 0.2s ease-in-out;
}

[data-sidebar="menu-button"]:hover .lucide-chevron-left {
  color: white !important;
}

/* Profile dropdown trigger enhanced effects */
[data-sidebar="menu-button"][data-state="open"] {
  background-color: #002443 !important;
  color: white !important;
}

[data-sidebar="menu-button"][data-state="open"] svg {
  color: white !important;
}

/* Profile Dropdown Menu Item Effects */
[data-radix-dropdown-menu-item] {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

[data-radix-dropdown-menu-item]:hover {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-item]:hover svg {
  color: white !important;
}

[data-radix-dropdown-menu-item]:hover span {
  color: white !important;
}

[data-radix-dropdown-menu-item]:focus {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-item]:focus svg {
  color: white !important;
}

[data-radix-dropdown-menu-item]:focus span {
  color: white !important;
}

/* Additional selectors for dropdown menu items */
[data-radix-dropdown-menu-content] [role="menuitem"]:hover {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:hover svg {
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:hover span {
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:focus {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:focus svg {
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:focus span {
  color: white !important;
}

/* Selected dropdown menu items (when on current page) */
[data-radix-dropdown-menu-item][data-current-page="true"] {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-item][data-current-page="true"] svg {
  color: white !important;
}

[data-radix-dropdown-menu-item][data-current-page="true"] span {
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"][data-current-page="true"] {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"][data-current-page="true"] svg {
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"][data-current-page="true"] span {
  color: white !important;
}

/* Sonner Toast RTL Styles */
[data-sonner-toaster] {
  direction: rtl !important;
}

[data-sonner-toast] {
  direction: rtl !important;
  text-align: right !important;
  font-family: var(--font-ibm-plex-arabic) !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
}

/* Enhanced Select Component RTL Styles */
[data-radix-select-trigger] {
  direction: rtl !important;
  text-align: right !important;
}

[data-radix-select-content] {
  direction: rtl !important;
  text-align: right !important;
}

[data-radix-select-item] {
  direction: rtl !important;
  text-align: right !important;
  justify-content: flex-start !important;
}

[data-radix-select-item] [data-radix-select-item-text] {
  text-align: right !important;
  width: 100% !important;
  direction: rtl !important;
}

[data-radix-select-item][data-highlighted] {
  background-color: var(--sidebar-accent) !important;
  color: white !important;
}

[data-radix-select-item][data-highlighted] svg {
  color: white !important;
}



/* Clickable Ticket Cards */
.ticket-card-link {
  text-decoration: none !important;
  color: inherit !important;
}

.ticket-card-link:hover {
  text-decoration: none !important;
}

.ticket-card-link .ticket-card {
  transition: all 0.2s ease-in-out;
}

.ticket-card-link:hover .ticket-card {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

[data-sonner-toast] [data-title] {
  text-align: right !important;
  direction: rtl !important;
}

[data-sonner-toast] [data-description] {
  text-align: right !important;
  direction: rtl !important;
}

/* Position toasts in bottom-left for RTL */
[data-sonner-toaster][data-x-position="left"] {
  left: 1rem !important;
  right: auto !important;
}

/* Success toast styling */
[data-sonner-toast][data-type="success"] {
  background-color: rgb(240 253 244) !important;
  border: 1px solid rgb(34 197 94) !important;
  color: rgb(21 128 61) !important;
}

/* Error toast styling */
[data-sonner-toast][data-type="error"] {
  background-color: rgb(254 242 242) !important;
  border: 1px solid rgb(239 68 68) !important;
  color: rgb(185 28 28) !important;
}

/* Warning toast styling */
[data-sonner-toast][data-type="warning"] {
  background-color: rgb(255 251 235) !important;
  border: 1px solid rgb(245 158 11) !important;
  color: rgb(146 64 14) !important;
}

/* Info toast styling */
[data-sonner-toast][data-type="info"] {
  background-color: rgb(239 246 255) !important;
  border: 1px solid rgb(59 130 246) !important;
  color: rgb(30 64 175) !important;
}

/* Loading toast styling */
[data-sonner-toast][data-type="loading"] {
  background-color: rgb(248 250 252) !important;
  border: 1px solid rgb(148 163 184) !important;
  color: rgb(71 85 105) !important;
}

/* Dark mode toast adjustments */
.dark [data-sonner-toast][data-type="success"] {
  background-color: rgb(20 83 45) !important;
  border: 1px solid rgb(34 197 94) !important;
  color: rgb(187 247 208) !important;
}

.dark [data-sonner-toast][data-type="error"] {
  background-color: rgb(127 29 29) !important;
  border: 1px solid rgb(239 68 68) !important;
  color: rgb(254 202 202) !important;
}

.dark [data-sonner-toast][data-type="warning"] {
  background-color: rgb(120 53 15) !important;
  border: 1px solid rgb(245 158 11) !important;
  color: rgb(254 240 138) !important;
}

.dark [data-sonner-toast][data-type="info"] {
  background-color: rgb(30 58 138) !important;
  border: 1px solid rgb(59 130 246) !important;
  color: rgb(191 219 254) !important;
}

.dark [data-sonner-toast][data-type="loading"] {
  background-color: rgb(51 65 85) !important;
  border: 1px solid rgb(148 163 184) !important;
  color: rgb(226 232 240) !important;
}

/* RTL Table Improvements */
.rtl table {
  direction: rtl;
}

.rtl th,
.rtl td {
  text-align: right;
}

.rtl th.text-center,
.rtl td.text-center {
  text-align: center;
}

.rtl .table-header-sort {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* Calendar RTL Improvements */
.rtl .rdp {
  direction: rtl;
}

.rtl .rdp-months {
  flex-direction: row-reverse;
}

.rtl .rdp-nav {
  flex-direction: row-reverse;
}

.rtl .rdp-nav_button_previous {
  right: 0;
  left: auto;
}

.rtl .rdp-nav_button_next {
  left: 0;
  right: auto;
}

/* Improved hover effects */
.hover-pointer {
  cursor: pointer;
}

.hover-pointer:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
}

/* Fix RTL layout overflow issues */
.rtl-container {
  direction: rtl;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.rtl-content {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Ensure tables don't overflow */
.rtl-table-container {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  direction: rtl;
}

.rtl-table-container table {
  width: 100%;
  min-width: 100%;
  direction: rtl;
}

/* Fix popover positioning for RTL */
[data-radix-popover-content] {
  max-width: calc(100vw - 2rem) !important;
}

/* Ensure cards don't overflow */
.rtl-card {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* Fixed table layout to prevent overflow */
.table-fixed {
  table-layout: fixed !important;
  width: 100% !important;
  max-width: 100% !important;
}

.table-fixed th,
.table-fixed td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0.5rem 0.25rem !important;
}

.table-fixed th:first-child,
.table-fixed td:first-child {
  padding-right: 0.5rem !important;
}

.table-fixed th:last-child,
.table-fixed td:last-child {
  padding-left: 0.5rem !important;
}

/* Responsive table container */
@media (max-width: 768px) {
  .table-fixed {
    font-size: 0.75rem;
  }

  .table-fixed th,
  .table-fixed td {
    padding: 0.25rem 0.125rem !important;
  }
}

/* Ensure table container doesn't exceed viewport */
.rtl-table-container {
  max-width: 100vw;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Compact table styling */
.compact-table th {
  font-size: 0.75rem;
  padding: 0.5rem 0.25rem;
  white-space: nowrap;
}

.compact-table td {
  font-size: 0.75rem;
  padding: 0.25rem;
  white-space: nowrap;
}

/* Sales tracking table specific styles */
.sales-tracking-table {
  min-width: 1200px;
  width: 1200px;
  table-layout: fixed;
}

.sales-tracking-table th,
.sales-tracking-table td {
  white-space: nowrap;
  padding: 0.5rem 0.5rem;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Basic table styling for scrollable tables */
table[style*="1400px"] {
  table-layout: fixed;
}

table[style*="1400px"] th,
table[style*="1400px"] td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0.5rem;
}

/* Column widths for 1400px table */
table[style*="1400px"] th:nth-child(1),
table[style*="1400px"] td:nth-child(1) {
  width: 120px; /* Date */
}

table[style*="1400px"] th:nth-child(2),
table[style*="1400px"] td:nth-child(2) {
  width: 220px; /* Employee */
}

table[style*="1400px"] th:nth-child(3),
table[style*="1400px"] td:nth-child(3),
table[style*="1400px"] th:nth-child(4),
table[style*="1400px"] td:nth-child(4) {
  width: 140px; /* Area, Team */
}

table[style*="1400px"] th:nth-child(5),
table[style*="1400px"] td:nth-child(5),
table[style*="1400px"] th:nth-child(6),
table[style*="1400px"] td:nth-child(6),
table[style*="1400px"] th:nth-child(7),
table[style*="1400px"] td:nth-child(7) {
  width: 150px; /* Amount columns */
}

table[style*="1400px"] th:nth-child(8),
table[style*="1400px"] td:nth-child(8),
table[style*="1400px"] th:nth-child(9),
table[style*="1400px"] td:nth-child(9) {
  width: 120px; /* Hours, Performance */
}

table[style*="1400px"] th:nth-child(10),
table[style*="1400px"] td:nth-child(10) {
  width: 100px; /* Actions */
}

/* Enhanced scrollbar for table containers */
.overflow-x-auto {
  scrollbar-width: auto !important;
  scrollbar-color: #002443 #e2e8f0 !important;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 12px !important;
  background: #f8fafc;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #e2e8f0 !important;
  border-radius: 6px;
  border: 1px solid #cbd5e1;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #002443 !important;
  border-radius: 6px;
  border: 2px solid #ffffff;
  transition: all 200ms ease;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #003a5f !important;
}

.overflow-x-auto::-webkit-scrollbar-corner {
  background: #f8fafc;
}

/* Force scrollbar to always be visible */
.table-scroll-container {
  overflow-x: scroll !important;
  overflow-y: visible !important;
  scrollbar-width: thick !important;
  scrollbar-color: #002443 #f1f5f9 !important;
  -webkit-overflow-scrolling: touch;
}

.table-scroll-container::-webkit-scrollbar {
  height: 16px !important;
  background: #f8fafc !important;
  border-radius: 0 0 8px 8px !important;
  display: block !important;
}

.table-scroll-container::-webkit-scrollbar-track {
  background: #e2e8f0 !important;
  border-radius: 0 0 8px 8px !important;
  border: 2px solid #cbd5e1 !important;
  display: block !important;
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background: #002443 !important;
  border-radius: 8px !important;
  border: 2px solid #ffffff !important;
  box-shadow: 0 2px 4px rgba(0, 36, 67, 0.3) !important;
  transition: all 200ms ease !important;
  display: block !important;
  min-width: 30px !important;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #003a5f !important;
  box-shadow: 0 3px 6px rgba(0, 36, 67, 0.4) !important;
  transform: scaleY(1.1) !important;
}

.table-scroll-container::-webkit-scrollbar-corner {
  background: #f8fafc !important;
  display: block !important;
}

/* Alternative scrollbar approach for better visibility */
.force-scrollbar {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  scrollbar-width: thick !important;
  scrollbar-color: #002443 #e2e8f0 !important;
}

.force-scrollbar::-webkit-scrollbar {
  height: 20px !important;
  background-color: #f1f5f9 !important;
  border-radius: 10px !important;
}

.force-scrollbar::-webkit-scrollbar-track {
  background-color: #e2e8f0 !important;
  border-radius: 10px !important;
  border: 3px solid #cbd5e1 !important;
}

.force-scrollbar::-webkit-scrollbar-thumb {
  background-color: #002443 !important;
  border-radius: 10px !important;
  border: 3px solid #ffffff !important;
  box-shadow: inset 0 0 0 1px #001a2e !important;
}

.force-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #003a5f !important;
}

/* Enhanced number display in tables */
.number-display {
  font-family: 'IBM Plex Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

.currency-amount {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  color: #0c4a6e;
  font-weight: 600;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 200ms ease;
}

.currency-amount:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.positive-amount {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
  color: #15803d;
}

.positive-amount:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
}

.negative-amount {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border: 1px solid #fca5a5;
  color: #dc2626;
}

.negative-amount:hover {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
}

.percentage-badge {
  background: #f1f5f9;
  color: #475569;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #e2e8f0;
}

/* Table container constraints */
.sales-tracking-table-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  -webkit-overflow-scrolling: touch;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--border));
}

.sales-tracking-scroll-wrapper {
  width: 100%;
  max-width: 100%;
  overflow-x: auto !important;
  overflow-y: visible;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: auto;
  scrollbar-color: hsl(var(--primary) / 0.7) hsl(var(--muted) / 0.2);
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  background: hsl(var(--background));
  position: relative;
  display: block;
}

.sales-tracking-scroll-wrapper::-webkit-scrollbar {
  height: 12px;
  width: 12px;
}

.sales-tracking-scroll-wrapper::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 6px;
  margin: 2px;
}

.sales-tracking-scroll-wrapper::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.8);
  border-radius: 6px;
  transition: background-color 200ms ease;
  border: 1px solid hsl(var(--background));
}

.sales-tracking-scroll-wrapper::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

.sales-tracking-scroll-wrapper::-webkit-scrollbar-corner {
  background: hsl(var(--muted) / 0.2);
}

/* Force horizontal scrolling when content overflows */
.sales-tracking-scroll-wrapper {
  display: block;
  contain: layout;
}

.sales-tracking-scroll-wrapper .sales-tracking-table {
  display: table;
  width: 1200px !important;
  min-width: 1200px !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .sales-tracking-table {
    min-width: 900px;
  }

  .sales-tracking-table .employee-column {
    min-width: 160px;
  }

  .sales-tracking-table .area-column,
  .sales-tracking-table .team-column {
    min-width: 110px;
  }
}

@media (max-width: 1024px) {
  .sales-tracking-table {
    min-width: 800px;
  }

  .sales-tracking-table .employee-column {
    min-width: 140px;
  }

  .sales-tracking-table .area-column,
  .sales-tracking-table .team-column {
    min-width: 100px;
  }

  .sales-tracking-table .amount-column {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .sales-tracking-table {
    min-width: 700px;
  }

  .sales-tracking-table th,
  .sales-tracking-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }

  .sales-tracking-table .employee-column {
    min-width: 120px;
  }

  .sales-tracking-table .area-column,
  .sales-tracking-table .team-column {
    min-width: 90px;
  }

  .sales-tracking-table .amount-column {
    min-width: 90px;
  }
}

/* Ensure page content doesn't overflow */
.page-container {
  max-width: 100vw;
  overflow-x: hidden;
}

.card-content-table {
  max-width: 100%;
  overflow: hidden;
}

/* Ensure main content area respects boundaries */
main {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Card containers should not overflow */
.card {
  max-width: 100%;
  overflow: hidden;
}

/* Specific fixes for dashboard layout */
.dashboard-content {
  max-width: 100%;
  overflow-x: hidden;
}

.dashboard-content .card {
  max-width: 100%;
}

/* Better spacing for Arabic text */
.arabic-text {
  font-family: 'IBM Plex Arabic', sans-serif;
  line-height: 1.6;
}

/* Enhanced table styling */
.enhanced-table {
  border-collapse: separate;
  border-spacing: 0;
}

.enhanced-table th {
  background-color: rgba(0, 0, 0, 0.02);
  font-weight: 600;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.enhanced-table td {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.enhanced-table tr:hover td {
  background-color: rgba(0, 0, 0, 0.02);
}
