import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requireAuth } from '@/lib/auth'

// GET - List salary calculations with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()
    const supabase = await createClient()

    const searchParams = request.nextUrl.searchParams
    const employee_id = searchParams.get('employee_id')
    const year = searchParams.get('year')
    const month = searchParams.get('month')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50)
    const offset = (page - 1) * limit

    // Check permissions
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role, area_id, team_id')
      .eq('id', user.id)
      .single()

    if (!userProfile) {
      return NextResponse.json(
        { error: 'ملف المستخدم غير موجود' },
        { status: 404 }
      )
    }

    let query = supabase
      .from('monthly_salary_calculations')
      .select(`
        *,
        employee:profiles!monthly_salary_calculations_employee_id_fkey(
          id, full_name, email, role, area_id, team_id
        ),
        deductions:salary_deductions(
          id, deduction_type, amount, description
        )
      `, { count: 'exact' })

    // Apply role-based filtering
    if (userProfile.role === 'sales_employee') {
      // Sales employees can only see their own salary records
      query = query.eq('employee_id', user.id)
    } else if (userProfile.role === 'team_manager') {
      // Team managers can see their team members' salaries
      const { data: teamMembers } = await supabase
        .from('profiles')
        .select('id')
        .eq('team_id', userProfile.team_id)

      if (teamMembers && teamMembers.length > 0) {
        const memberIds = teamMembers.map(member => member.id)
        query = query.in('employee_id', memberIds)
      } else {
        // No team members, return empty result
        return NextResponse.json({
          salaries: [],
          total: 0,
          page,
          limit,
          totalPages: 0
        })
      }
    } else if (userProfile.role === 'area_manager') {
      // Area managers can see their area employees' salaries
      const { data: areaMembers } = await supabase
        .from('profiles')
        .select('id')
        .eq('area_id', userProfile.area_id)

      if (areaMembers && areaMembers.length > 0) {
        const memberIds = areaMembers.map(member => member.id)
        query = query.in('employee_id', memberIds)
      } else {
        // No area members, return empty result
        return NextResponse.json({
          salaries: [],
          total: 0,
          page,
          limit,
          totalPages: 0
        })
      }
    }
    // System admins can see all salary records (no additional filtering)

    // Apply filters
    if (employee_id) {
      query = query.eq('employee_id', employee_id)
    }

    if (year) {
      const startDate = `${year}-01-01`
      const endDate = `${year}-12-31`
      query = query.gte('salary_month', startDate).lte('salary_month', endDate)
    }

    if (month && year) {
      const salaryMonth = `${year}-${month.toString().padStart(2, '0')}-01`
      query = query.eq('salary_month', salaryMonth)
    }

    // Apply pagination and ordering
    const { data: salaries, error, count } = await query
      .order('salary_month', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching salary records:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب بيانات الرواتب' },
        { status: 500 }
      )
    }

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      salaries: salaries || [],
      total: count || 0,
      page,
      limit,
      totalPages
    })

  } catch (error: unknown) {
    console.error('Error in salary list API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
