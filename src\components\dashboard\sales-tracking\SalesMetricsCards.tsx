'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  Clock,
  AlertTriangle,
  Target,
  Award
} from 'lucide-react'

interface SalesData {
  id: string
  closing_date: string
  user_id: string
  total_sales_amount: number
  cash_delivered: number
  deficit_amount: number
  advances_amount: number
  price_breaks_amount: number
  working_hours?: number
  employee: {
    id: string
    full_name: string
    email: string
    role: string
    area_id?: string
    team_id?: string
  }
  area?: {
    id: string
    name: string
  }
  team?: {
    id: string
    name: string
  }
}

interface SalesMetricsCardsProps {
  data: SalesData[]
  loading: boolean
}

export function SalesMetricsCards({ data, loading }: SalesMetricsCardsProps) {
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(8)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted rounded animate-pulse"></div>
              </CardTitle>
              <div className="h-4 w-4 bg-muted rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded animate-pulse mb-2"></div>
              <div className="h-3 bg-muted rounded animate-pulse w-3/4"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Calculate metrics
  const totalSales = data.reduce((sum, item) => sum + item.total_sales_amount, 0)
  const totalCashDelivered = data.reduce((sum, item) => sum + item.cash_delivered, 0)
  const totalDeficit = data.reduce((sum, item) => sum + item.deficit_amount, 0)
  const totalAdvances = data.reduce((sum, item) => sum + item.advances_amount, 0)
  const totalDueDeduction = totalDeficit + totalAdvances // الخصم المستحق = العجز + السلف
  const totalPriceBreaks = data.reduce((sum, item) => sum + item.price_breaks_amount, 0)
  const totalWorkingHours = data.reduce((sum, item) => sum + (item.working_hours || 0), 0)
  
  const uniqueEmployees = new Set(data.map(item => item.user_id)).size
  const averageSalesPerDay = data.length > 0 ? totalSales / data.length : 0
  const averageWorkingHours = data.length > 0 ? totalWorkingHours / data.length : 0
  const salesEfficiency = totalWorkingHours > 0 ? totalSales / totalWorkingHours : 0
  
  // Calculate trends (comparing first half vs second half of data)
  const midPoint = Math.floor(data.length / 2)
  const firstHalf = data.slice(0, midPoint)
  const secondHalf = data.slice(midPoint)
  
  const firstHalfAvg = firstHalf.length > 0 
    ? firstHalf.reduce((sum, item) => sum + item.total_sales_amount, 0) / firstHalf.length 
    : 0
  const secondHalfAvg = secondHalf.length > 0 
    ? secondHalf.reduce((sum, item) => sum + item.total_sales_amount, 0) / secondHalf.length 
    : 0
  
  const salesTrend = firstHalfAvg > 0 ? ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100 : 0
  
  // Find top performer
  const employeePerformance = data.reduce((acc, item) => {
    if (!acc[item.user_id]) {
      acc[item.user_id] = {
        name: item.employee.full_name,
        totalSales: 0,
        count: 0
      }
    }
    acc[item.user_id].totalSales += item.total_sales_amount
    acc[item.user_id].count += 1
    return acc
  }, {} as Record<string, { name: string; totalSales: number; count: number }>)
  
  const topPerformer = Object.values(employeePerformance)
    .sort((a, b) => b.totalSales - a.totalSales)[0]

  // للمبالغ المالية الفعلية - دقة كاملة
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ر.س`
  }

  // للعروض المدمجة في البطاقات - اختصارات مقبولة
  const formatDisplayCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)} مليون ر.س`
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(0)} ألف ر.س`
    } else {
      return `${amount.toFixed(0)} ر.س`
    }
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getTrendIcon = (trend: number) => {
    if (trend > 0) {
      return <TrendingUp className="h-4 w-4 text-green-600" />
    } else if (trend < 0) {
      return <TrendingDown className="h-4 w-4 text-red-600" />
    } else {
      return <TrendingUp className="h-4 w-4 text-gray-400" />
    }
  }

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-green-600'
    if (trend < 0) return 'text-red-600'
    return 'text-gray-500'
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Sales */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي المبيعات</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold" title={formatCurrency(totalSales)}>{formatDisplayCurrency(totalSales)}</div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            {getTrendIcon(salesTrend)}
            <span className={getTrendColor(salesTrend)}>
              {formatPercentage(Math.abs(salesTrend))}
            </span>
            <span>من الفترة السابقة</span>
          </div>
        </CardContent>
      </Card>

      {/* Average Sales Per Day */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">متوسط المبيعات اليومية</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold" title={formatCurrency(averageSalesPerDay)}>{formatDisplayCurrency(averageSalesPerDay)}</div>
          <p className="text-xs text-muted-foreground">
            من {data.length} يوم عمل
          </p>
        </CardContent>
      </Card>

      {/* Total Due Deduction */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي الخصم المستحق</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600" title={formatCurrency(totalDueDeduction)}>{formatDisplayCurrency(totalDueDeduction)}</div>
          <p className="text-xs text-muted-foreground">
            {((totalDueDeduction / totalSales) * 100).toFixed(1)}% من المبيعات
          </p>
          <div className="text-xs text-muted-foreground mt-1">
            عجز: {formatDisplayCurrency(totalDeficit)} | سلف: {formatDisplayCurrency(totalAdvances)}
          </div>
        </CardContent>
      </Card>

      {/* Price Breaks (Non-deductible) */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">كسر الأسعار</CardTitle>
          <DollarSign className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600" title={formatCurrency(totalPriceBreaks)}>{formatDisplayCurrency(totalPriceBreaks)}</div>
          <p className="text-xs text-muted-foreground">
            غير مخصوم من الراتب
          </p>
          <div className="text-xs text-blue-600 mt-1">
            {totalSales > 0 ? ((totalPriceBreaks / totalSales) * 100).toFixed(1) : '0'}% من المبيعات
          </div>
        </CardContent>
      </Card>

      {/* Total Working Hours */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي ساعات العمل</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalWorkingHours.toFixed(1)} ساعة</div>
          <p className="text-xs text-muted-foreground">
            متوسط {averageWorkingHours.toFixed(1)} ساعة/يوم
          </p>
        </CardContent>
      </Card>

      {/* Sales Efficiency */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">كفاءة المبيعات</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold" title={formatCurrency(salesEfficiency)}>{formatDisplayCurrency(salesEfficiency)}</div>
          <p className="text-xs text-muted-foreground">
            ر.س لكل ساعة عمل
          </p>
        </CardContent>
      </Card>

      {/* Cash Delivered */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">المبلغ المسلم</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold" title={formatCurrency(totalCashDelivered)}>{formatDisplayCurrency(totalCashDelivered)}</div>
          <div className="flex items-center gap-2 text-xs">
            <Badge 
              variant={totalCashDelivered >= totalSales ? "default" : "destructive"}
              className="text-xs"
            >
              {((totalCashDelivered / totalSales) * 100).toFixed(1)}%
            </Badge>
            <span className="text-muted-foreground">من المبيعات</span>
          </div>
        </CardContent>
      </Card>

      {/* Top Performer */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">أفضل أداء</CardTitle>
          <Award className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-lg font-bold truncate">
            {topPerformer?.name || 'لا يوجد'}
          </div>
          <p className="text-xs text-muted-foreground" title={topPerformer ? formatCurrency(topPerformer.totalSales) : ''}>
            {topPerformer ? formatDisplayCurrency(topPerformer.totalSales) : 'لا توجد بيانات'}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
