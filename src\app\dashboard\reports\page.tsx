import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { ReportsManagementDashboard } from '@/components/dashboard/reports/ReportsManagementDashboard'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import Link from 'next/link'

export default async function ReportsPage() {
  const supabase = createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/auth/login')
  }

  // Get user profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/auth/login')
  }

  // Check if user has access to reports
  const allowedRoles = ['system_admin', 'area_manager', 'team_manager']
  if (!allowedRoles.includes(profile.role)) {
    redirect('/dashboard')
  }

  return (
    <div className="container mx-auto py-6" dir="rtl">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">الرئيسية</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>التقارير والتصدير</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <ReportsManagementDashboard
        userRole={profile.role}
        userId={profile.id}
        userAreaId={profile.area_id}
        userTeamId={profile.team_id}
      />
    </div>
  )
}
