-- Fix daily_closings table columns
-- This migration ensures all required columns exist with proper types

-- Add missing columns if they don't exist
ALTER TABLE public.daily_closings 
ADD COLUMN IF NOT EXISTS total_sales_amount DECIMAL(10,2) DEFAULT 0 CHECK (total_sales_amount >= 0),
ADD COLUMN IF NOT EXISTS cash_delivered DECIMAL(10,2) DEFAULT 0 CHECK (cash_delivered >= 0),
ADD COLUMN IF NOT EXISTS advances_amount DECIMAL(10,2) DEFAULT 0 CHECK (advances_amount >= 0),
ADD COLUMN IF NOT EXISTS price_breaks_amount DECIMAL(10,2) DEFAULT 0 CHECK (price_breaks_amount >= 0),
ADD COLUMN IF NOT EXISTS deficit_amount DECIMAL(10,2) DEFAULT 0 CHECK (deficit_amount >= 0),
ADD COLUMN IF NOT EXISTS cash_confirmed BOOLEAN DEFAULT false;

-- Update any existing records to have proper default values
UPDATE public.daily_closings 
SET 
    total_sales_amount = COALESCE(total_sales_amount, 0),
    cash_delivered = COALESCE(cash_delivered, 0),
    advances_amount = COALESCE(advances_amount, 0),
    price_breaks_amount = COALESCE(price_breaks_amount, 0),
    deficit_amount = COALESCE(deficit_amount, 0),
    cash_confirmed = COALESCE(cash_confirmed, false)
WHERE 
    total_sales_amount IS NULL OR
    cash_delivered IS NULL OR
    advances_amount IS NULL OR
    price_breaks_amount IS NULL OR
    deficit_amount IS NULL OR
    cash_confirmed IS NULL;

-- Ensure the columns are NOT NULL with proper defaults
ALTER TABLE public.daily_closings 
ALTER COLUMN total_sales_amount SET NOT NULL,
ALTER COLUMN total_sales_amount SET DEFAULT 0,
ALTER COLUMN cash_delivered SET NOT NULL,
ALTER COLUMN cash_delivered SET DEFAULT 0,
ALTER COLUMN advances_amount SET NOT NULL,
ALTER COLUMN advances_amount SET DEFAULT 0,
ALTER COLUMN price_breaks_amount SET NOT NULL,
ALTER COLUMN price_breaks_amount SET DEFAULT 0,
ALTER COLUMN deficit_amount SET NOT NULL,
ALTER COLUMN deficit_amount SET DEFAULT 0,
ALTER COLUMN cash_confirmed SET NOT NULL,
ALTER COLUMN cash_confirmed SET DEFAULT false;

-- Add helpful comment
COMMENT ON TABLE public.daily_closings IS 'Daily closing records for sales employees with financial tracking';
COMMENT ON COLUMN public.daily_closings.total_sales_amount IS 'Total sales amount for the day';
COMMENT ON COLUMN public.daily_closings.cash_delivered IS 'Cash amount delivered by employee';
COMMENT ON COLUMN public.daily_closings.advances_amount IS 'Employee advances (سلف) - deductible from salary';
COMMENT ON COLUMN public.daily_closings.price_breaks_amount IS 'Price breaks (كسر السعر) - not deductible from salary';
COMMENT ON COLUMN public.daily_closings.deficit_amount IS 'Deficit amount (عجز) - deductible from salary';
COMMENT ON COLUMN public.daily_closings.cash_confirmed IS 'Whether cash amount has been confirmed';
