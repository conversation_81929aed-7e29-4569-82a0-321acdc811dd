'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'
import type { Database } from '@/lib/supabase'

type SalaryCalculation = Database['public']['Tables']['monthly_salary_calculations']['Row']

interface SalaryMetricsCardsProps {
  data: SalaryCalculation[]
  loading: boolean
  userRole: string
}

export function SalaryMetricsCards({ data, loading, userRole }: SalaryMetricsCardsProps) {
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(8)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted rounded animate-pulse"></div>
              </CardTitle>
              <div className="h-4 w-4 bg-muted rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded animate-pulse mb-2"></div>
              <div className="h-3 bg-muted rounded animate-pulse w-3/4"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Calculate metrics
  const totalSalaries = data.reduce((sum, item) => sum + (item.final_salary || 0), 0)
  const totalBaseSalaries = data.reduce((sum, item) => sum + (item.base_salary || 0), 0)
  const totalDeductions = data.reduce((sum, item) => sum + (item.total_deductions || 0), 0)
  const totalAdvanceDeductions = data.reduce((sum, item) => sum + (item.advance_deductions || 0), 0)
  const totalDeficitDeductions = data.reduce((sum, item) => sum + (item.deficit_deductions || 0), 0)
  
  const uniqueEmployees = new Set(data.map(item => item.employee_id)).size
  const currentMonth = new Date().getMonth()
  const currentYear = new Date().getFullYear()
  
  const currentMonthRecords = data.filter(item => {
    const recordDate = new Date(item.calculation_date)
    return recordDate.getMonth() === currentMonth && recordDate.getFullYear() === currentYear
  })
  
  const previousMonthRecords = data.filter(item => {
    const recordDate = new Date(item.calculation_date)
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear
    return recordDate.getMonth() === prevMonth && recordDate.getFullYear() === prevYear
  })

  const currentMonthTotal = currentMonthRecords.reduce((sum, item) => sum + (item.final_salary || 0), 0)
  const previousMonthTotal = previousMonthRecords.reduce((sum, item) => sum + (item.final_salary || 0), 0)
  
  const monthlyTrend = previousMonthTotal > 0 
    ? ((currentMonthTotal - previousMonthTotal) / previousMonthTotal) * 100 
    : 0

  const averageSalary = data.length > 0 ? totalSalaries / data.length : 0
  const averageWorkingHours = data.length > 0 
    ? data.reduce((sum, item) => sum + (item.actual_working_hours || 0), 0) / data.length 
    : 0

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ر.س`
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getTrendIcon = (trend: number) => {
    if (trend > 0) {
      return <TrendingUp className="h-4 w-4 text-green-600" />
    } else if (trend < 0) {
      return <TrendingDown className="h-4 w-4 text-red-600" />
    } else {
      return <TrendingUp className="h-4 w-4 text-gray-400" />
    }
  }

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-green-600'
    if (trend < 0) return 'text-red-600'
    return 'text-gray-500'
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Salaries */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي الرواتب</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(totalSalaries)}</div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            {getTrendIcon(monthlyTrend)}
            <span className={getTrendColor(monthlyTrend)}>
              {formatPercentage(Math.abs(monthlyTrend))}
            </span>
            <span>من الشهر السابق</span>
          </div>
        </CardContent>
      </Card>

      {/* Average Salary */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">متوسط الراتب</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(averageSalary)}</div>
          <p className="text-xs text-muted-foreground">
            لـ {uniqueEmployees} موظف
          </p>
        </CardContent>
      </Card>

      {/* Total Deductions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي الخصومات</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{formatCurrency(totalDeductions)}</div>
          <p className="text-xs text-muted-foreground">
            {((totalDeductions / totalBaseSalaries) * 100).toFixed(1)}% من الراتب الأساسي
          </p>
        </CardContent>
      </Card>

      {/* Current Month Records */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">رواتب الشهر الحالي</CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{currentMonthRecords.length}</div>
          <p className="text-xs text-muted-foreground">
            راتب محسوب
          </p>
        </CardContent>
      </Card>

      {/* Advance Deductions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">خصومات السلف</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">{formatCurrency(totalAdvanceDeductions)}</div>
          <p className="text-xs text-muted-foreground">
            من إجمالي الخصومات
          </p>
        </CardContent>
      </Card>

      {/* Deficit Deductions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">خصومات العجز</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{formatCurrency(totalDeficitDeductions)}</div>
          <p className="text-xs text-muted-foreground">
            من إجمالي الخصومات
          </p>
        </CardContent>
      </Card>

      {/* Average Working Hours */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">متوسط ساعات العمل</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{averageWorkingHours.toFixed(1)} ساعة</div>
          <p className="text-xs text-muted-foreground">
            شهرياً لكل موظف
          </p>
        </CardContent>
      </Card>

      {/* Salary Efficiency */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">كفاءة الرواتب</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {((totalSalaries / totalBaseSalaries) * 100).toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            من الراتب الأساسي
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
