'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DateRangePicker } from '@/components/ui/date-range-picker'
import { 
  FileText, 
  Download, 
  Calendar,
  Users,
  DollarSign,
  BarChart3,
  TrendingUp,
  FileSpreadsheet,
  FileDown
} from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { toast } from '@/hooks/use-toast'
import { 
  exportToCSV, 
  exportToExcel, 
  exportSalaryData, 
  exportSalesData, 
  exportAdvancesData 
} from '@/lib/export-utils'
import type { Database } from '@/lib/supabase'
import { addDays, subDays, startOfMonth, endOfMonth } from 'date-fns'

type Profile = Database['public']['Tables']['profiles']['Row']

interface ReportsManagementDashboardProps {
  userRole: string
  userId: string
  userAreaId?: string
  userTeamId?: string
}

interface DateRange {
  from: Date
  to: Date
}

export function ReportsManagementDashboard({ 
  userRole, 
  userId, 
  userAreaId, 
  userTeamId 
}: ReportsManagementDashboardProps) {
  const [loading, setLoading] = useState(false)
  const [dateRange, setDateRange] = useState<DateRange>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date())
  })
  const [selectedEmployee, setSelectedEmployee] = useState('all')
  const [selectedArea, setSelectedArea] = useState('all')
  const [selectedTeam, setSelectedTeam] = useState('all')
  const [employees, setEmployees] = useState<Profile[]>([])
  const [areas, setAreas] = useState<any[]>([])
  const [teams, setTeams] = useState<any[]>([])

  const supabase = createClient()

  useEffect(() => {
    fetchFilterData()
  }, [])

  const fetchFilterData = async () => {
    try {
      // Fetch employees based on role
      let employeesQuery = supabase
        .from('profiles')
        .select('id, full_name, email, role, area_id, team_id')
        .order('full_name')

      if (userRole === 'team_manager' && userTeamId) {
        employeesQuery = employeesQuery.eq('team_id', userTeamId)
      } else if (userRole === 'area_manager' && userAreaId) {
        employeesQuery = employeesQuery.eq('area_id', userAreaId)
      }

      const { data: employeesData } = await employeesQuery
      setEmployees(employeesData || [])

      // Fetch areas (for system admin and area managers)
      if (['system_admin', 'area_manager'].includes(userRole)) {
        const { data: areasData } = await supabase
          .from('areas')
          .select('id, name')
          .order('name')
        setAreas(areasData || [])
      }

      // Fetch teams
      let teamsQuery = supabase
        .from('teams')
        .select('id, name, area_id')
        .order('name')

      if (userRole === 'area_manager' && userAreaId) {
        teamsQuery = teamsQuery.eq('area_id', userAreaId)
      }

      const { data: teamsData } = await teamsQuery
      setTeams(teamsData || [])

    } catch (error) {
      console.error('Error fetching filter data:', error)
    }
  }

  const generateSalaryReport = async (format: 'csv' | 'excel') => {
    try {
      setLoading(true)

      let query = supabase
        .from('monthly_salary_calculations')
        .select(`
          *,
          employee:profiles!monthly_salary_calculations_employee_id_fkey(
            id, full_name, email, role, area_id, team_id
          )
        `)
        .gte('calculation_date', dateRange.from.toISOString())
        .lte('calculation_date', dateRange.to.toISOString())
        .order('calculation_date', { ascending: false })

      // Apply filters
      if (selectedEmployee && selectedEmployee !== 'all') {
        query = query.eq('employee_id', selectedEmployee)
      }

      // Apply role-based filtering
      if (userRole === 'team_manager' && userTeamId) {
        const teamMemberIds = employees
          .filter(emp => emp.team_id === userTeamId)
          .map(emp => emp.id)
        if (teamMemberIds.length > 0) {
          query = query.in('employee_id', teamMemberIds)
        }
      } else if (userRole === 'area_manager' && userAreaId) {
        const areaMemberIds = employees
          .filter(emp => emp.area_id === userAreaId)
          .map(emp => emp.id)
        if (areaMemberIds.length > 0) {
          query = query.in('employee_id', areaMemberIds)
        }
      }

      const { data, error } = await query

      if (error) throw error

      if (!data || data.length === 0) {
        toast.error('لا توجد بيانات رواتب للفترة المحددة')
        return
      }

      const { columns } = exportSalaryData(data)
      const filename = `salary_report_${dateRange.from.toISOString().split('T')[0]}_to_${dateRange.to.toISOString().split('T')[0]}`

      if (format === 'csv') {
        exportToCSV(data, columns, { filename })
      } else {
        exportToExcel(data, columns, { filename })
      }

      toast.success(`تم تصدير تقرير الرواتب بصيغة ${format.toUpperCase()} بنجاح`)

    } catch (error) {
      console.error('Error generating salary report:', error)
      toast.error('حدث خطأ في إنشاء تقرير الرواتب')
    } finally {
      setLoading(false)
    }
  }

  const generateSalesReport = async (format: 'csv' | 'excel') => {
    try {
      setLoading(true)

      // Build query parameters for the API
      const params = new URLSearchParams()

      if (dateRange.from) {
        params.append('dateFrom', dateRange.from.toISOString().split('T')[0])
      }
      if (dateRange.to) {
        params.append('dateTo', dateRange.to.toISOString().split('T')[0])
      }
      if (selectedEmployee && selectedEmployee !== 'all') {
        params.append('selectedEmployee', selectedEmployee)
      }
      if (selectedArea && selectedArea !== 'all') {
        params.append('selectedArea', selectedArea)
      }
      if (selectedTeam && selectedTeam !== 'all') {
        params.append('selectedTeam', selectedTeam)
      }

      const url = `/api/sales-data?${params.toString()}`
      const response = await fetch(url)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`)
      }

      if (!result.success) {
        throw new Error(result.error || 'API request failed')
      }

      const data = result.data

      if (!data || data.length === 0) {
        toast.error('لا توجد بيانات مبيعات للفترة المحددة')
        return
      }

      // Transform data for sales export with all fields
      const salesData = data.map((item: any) => ({
        date: item.closing_date,
        employee: {
          full_name: item.employee.full_name,
          email: item.employee.email,
          role: item.employee.role,
          area: {
            name: item.area?.name || 'غير محدد'
          },
          team: {
            name: item.team?.name || 'غير محدد'
          }
        },
        check_in_time: item.check_in_time || '',
        check_out_time: item.check_out_time || '',
        working_hours: item.working_hours || 0,
        sales_amount: item.total_sales_amount || 0,
        cash_delivered: item.cash_delivered || 0,
        advances_amount: item.advances_amount || 0,
        price_breaks_amount: item.price_breaks_amount || 0,
        deficit_amount: item.deficit_amount || 0,
        cash_confirmed: item.cash_confirmed || false,
        efficiency: item.working_hours ? (item.working_hours / 8) * 100 : 0,
        performance_rating: item.working_hours >= 8 ? 'excellent' :
                           item.working_hours >= 6 ? 'good' :
                           item.working_hours >= 4 ? 'average' : 'poor',
        notes: item.notes || ''
      }))

      const { columns } = exportSalesData(salesData)
      const filename = `sales_report_${dateRange.from.toISOString().split('T')[0]}_to_${dateRange.to.toISOString().split('T')[0]}`

      if (format === 'csv') {
        exportToCSV(salesData, columns, { filename })
      } else {
        exportToExcel(salesData, columns, { filename })
      }

      toast.success(`تم تصدير تقرير المبيعات بصيغة ${format.toUpperCase()} بنجاح`)

    } catch (error) {
      console.error('Error generating sales report:', error)
      toast.error('حدث خطأ في إنشاء تقرير المبيعات')
    } finally {
      setLoading(false)
    }
  }

  const generateAdvancesReport = async (format: 'csv' | 'excel') => {
    try {
      setLoading(true)

      let query = supabase
        .from('employee_advances')
        .select(`
          *,
          employee:profiles!employee_advances_employee_id_fkey(
            id, full_name, email, role, area_id, team_id
          )
        `)
        .gte('request_date', dateRange.from.toISOString())
        .lte('request_date', dateRange.to.toISOString())
        .order('request_date', { ascending: false })

      // Apply filters
      if (selectedEmployee && selectedEmployee !== 'all') {
        query = query.eq('employee_id', selectedEmployee)
      }

      // Apply role-based filtering
      if (userRole === 'team_manager' && userTeamId) {
        const teamMemberIds = employees
          .filter(emp => emp.team_id === userTeamId)
          .map(emp => emp.id)
        if (teamMemberIds.length > 0) {
          query = query.in('employee_id', teamMemberIds)
        }
      } else if (userRole === 'area_manager' && userAreaId) {
        const areaMemberIds = employees
          .filter(emp => emp.area_id === userAreaId)
          .map(emp => emp.id)
        if (areaMemberIds.length > 0) {
          query = query.in('employee_id', areaMemberIds)
        }
      }

      const { data, error } = await query

      if (error) throw error

      if (!data || data.length === 0) {
        toast.error('لا توجد بيانات سلف للفترة المحددة')
        return
      }

      const { columns } = exportAdvancesData(data)
      const filename = `advances_report_${dateRange.from.toISOString().split('T')[0]}_to_${dateRange.to.toISOString().split('T')[0]}`

      if (format === 'csv') {
        exportToCSV(data, columns, { filename })
      } else {
        exportToExcel(data, columns, { filename })
      }

      toast.success(`تم تصدير تقرير السلف بصيغة ${format.toUpperCase()} بنجاح`)

    } catch (error) {
      console.error('Error generating advances report:', error)
      toast.error('حدث خطأ في إنشاء تقرير السلف')
    } finally {
      setLoading(false)
    }
  }

  const canViewReports = ['system_admin', 'area_manager', 'team_manager'].includes(userRole)

  if (!canViewReports) {
    return (
      <div className="text-center py-8" dir="rtl">
        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">ليس لديك صلاحية لعرض التقارير</p>
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">التقارير والتصدير</h2>
          <p className="text-muted-foreground">
            إنشاء وتصدير التقارير المالية والإدارية
          </p>
        </div>
        <Badge variant="secondary" className="gap-1">
          <BarChart3 className="h-4 w-4" />
          {userRole === 'system_admin' ? 'مدير النظام' : 
           userRole === 'area_manager' ? 'مدير منطقة' : 'مدير فريق'}
        </Badge>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            فلاتر التقارير
          </CardTitle>
          <CardDescription>
            حدد الفترة الزمنية والمعايير لإنشاء التقارير
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Date Range */}
            <div className="space-y-2">
              <label className="text-sm font-medium">الفترة الزمنية</label>
              <DateRangePicker
                date={dateRange}
                onDateChange={(range) => range && setDateRange(range)}
              />
            </div>

            {/* Employee Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">الموظف</label>
              <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع الموظفين" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الموظفين</SelectItem>
                  {employees.map(employee => (
                    <SelectItem key={employee.id} value={employee.id}>
                      {employee.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Area Filter (for system admin) */}
            {userRole === 'system_admin' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">المنطقة</label>
                <Select value={selectedArea} onValueChange={setSelectedArea}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع المناطق" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع المناطق</SelectItem>
                    {areas.map(area => (
                      <SelectItem key={area.id} value={area.id}>
                        {area.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Team Filter */}
            {['system_admin', 'area_manager'].includes(userRole) && (
              <div className="space-y-2">
                <label className="text-sm font-medium">الفريق</label>
                <Select value={selectedTeam} onValueChange={setSelectedTeam}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الفرق" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفرق</SelectItem>
                    {teams.map(team => (
                      <SelectItem key={team.id} value={team.id}>
                        {team.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Report Types */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* Salary Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              تقارير الرواتب
            </CardTitle>
            <CardDescription>
              تصدير بيانات الرواتب والخصومات
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex gap-2">
              <Button 
                onClick={() => generateSalaryReport('csv')}
                disabled={loading}
                variant="outline"
                className="flex-1 gap-2"
              >
                <FileText className="h-4 w-4" />
                CSV
              </Button>
              <Button 
                onClick={() => generateSalaryReport('excel')}
                disabled={loading}
                variant="outline"
                className="flex-1 gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Excel
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Sales Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              تقارير المبيعات
            </CardTitle>
            <CardDescription>
              تصدير بيانات المبيعات والأداء
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex gap-2">
              <Button 
                onClick={() => generateSalesReport('csv')}
                disabled={loading}
                variant="outline"
                className="flex-1 gap-2"
              >
                <FileText className="h-4 w-4" />
                CSV
              </Button>
              <Button 
                onClick={() => generateSalesReport('excel')}
                disabled={loading}
                variant="outline"
                className="flex-1 gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Excel
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Advances Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              تقارير السلف
            </CardTitle>
            <CardDescription>
              تصدير بيانات السلف والموافقات
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex gap-2">
              <Button 
                onClick={() => generateAdvancesReport('csv')}
                disabled={loading}
                variant="outline"
                className="flex-1 gap-2"
              >
                <FileText className="h-4 w-4" />
                CSV
              </Button>
              <Button 
                onClick={() => generateAdvancesReport('excel')}
                disabled={loading}
                variant="outline"
                className="flex-1 gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Excel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="py-8">
            <div className="flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">جاري إنشاء التقرير...</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
