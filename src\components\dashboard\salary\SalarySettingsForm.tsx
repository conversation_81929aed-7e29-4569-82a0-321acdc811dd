'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Settings, Save, AlertTriangle, CheckCircle, DollarSign } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { toast } from '@/hooks/use-toast'
import type { Database } from '@/lib/supabase'

type SalarySettings = Database['public']['Tables']['salary_settings']['Row']

interface SalarySettingsFormProps {
  currentSettings: SalarySettings | null
  onSettingsUpdate: (newSettings: SalarySettings) => void
}

export function SalarySettingsForm({ currentSettings, onSettingsUpdate }: SalarySettingsFormProps) {
  const [baseSalary, setBaseSalary] = useState(currentSettings?.base_salary || 3000)
  const [workingHoursPerDay, setWorkingHoursPerDay] = useState(currentSettings?.working_hours_per_day || 8)
  const [currency, setCurrency] = useState(currentSettings?.currency || 'SAR')
  const [effectiveFrom, setEffectiveFrom] = useState(
    currentSettings?.effective_from || new Date().toISOString().split('T')[0]
  )
  const [loading, setLoading] = useState(false)

  const supabase = createClient()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (baseSalary <= 0 || workingHoursPerDay <= 0) {
      toast.error('الراتب الأساسي وساعات العمل يجب أن تكون أكبر من صفر')
      return
    }

    try {
      setLoading(true)

      const response = await fetch('/api/salary/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          base_salary: baseSalary,
          working_hours_per_day: workingHoursPerDay,
          currency,
          effective_from: effectiveFrom
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في حفظ الإعدادات')
      }

      toast.success('تم حفظ إعدادات الراتب بنجاح')
      onSettingsUpdate(result.settings)

    } catch (error) {
      console.error('Error saving salary settings:', error)
      toast.error(error instanceof Error ? error.message : 'حدث خطأ في حفظ الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('ar-SA')} ر.س`
  }

  const calculateMonthlyHours = () => {
    // Average working days per month (excluding weekends)
    const workingDaysPerMonth = 22
    return workingHoursPerDay * workingDaysPerMonth
  }

  const calculateHourlyRate = () => {
    const monthlyHours = calculateMonthlyHours()
    return monthlyHours > 0 ? baseSalary / monthlyHours : 0
  }

  return (
    <div className="space-y-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            إعدادات الراتب
          </CardTitle>
          <CardDescription>
            تحديد الراتب الأساسي وساعات العمل المطلوبة للنظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="baseSalary">الراتب الأساسي</Label>
                <div className="relative">
                  <Input
                    id="baseSalary"
                    type="number"
                    min="0"
                    step="0.01"
                    value={baseSalary}
                    onChange={(e) => setBaseSalary(parseFloat(e.target.value) || 0)}
                    className="pr-12"
                    required
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                    ر.س
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  الراتب الأساسي الشهري للموظفين (افتراضي: 3000 ر.س)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="workingHours">ساعات العمل اليومية</Label>
                <div className="relative">
                  <Input
                    id="workingHours"
                    type="number"
                    min="0"
                    step="0.5"
                    value={workingHoursPerDay}
                    onChange={(e) => setWorkingHoursPerDay(parseFloat(e.target.value) || 0)}
                    className="pr-12"
                    required
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                    ساعة
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  عدد ساعات العمل المطلوبة يومياً (افتراضي: 8 ساعات)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">العملة</Label>
                <Select value={currency} onValueChange={setCurrency}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SAR">ريال سعودي (SAR)</SelectItem>
                    <SelectItem value="USD">دولار أمريكي (USD)</SelectItem>
                    <SelectItem value="EUR">يورو (EUR)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="effectiveFrom">تاريخ السريان</Label>
                <Input
                  id="effectiveFrom"
                  type="date"
                  value={effectiveFrom}
                  onChange={(e) => setEffectiveFrom(e.target.value)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  تاريخ بداية تطبيق هذه الإعدادات
                </p>
              </div>
            </div>

            <Separator />

            {/* Calculations Preview */}
            <div className="space-y-4">
              <h4 className="font-medium">معاينة الحسابات</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {formatCurrency(baseSalary)}
                      </div>
                      <div className="text-sm text-blue-700">الراتب الشهري</div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-green-50 border-green-200">
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {calculateMonthlyHours()} ساعة
                      </div>
                      <div className="text-sm text-green-700">ساعات العمل الشهرية</div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-purple-50 border-purple-200">
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {calculateHourlyRate().toFixed(2)} ر.س
                      </div>
                      <div className="text-sm text-purple-700">سعر الساعة</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <Separator />

            {/* Important Notes */}
            <div className="space-y-3">
              <h4 className="font-medium">ملاحظات مهمة</h4>
              <div className="space-y-2">
                <div className="flex items-start gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>سيتم حساب الرواتب بناءً على أيام العمل الفعلية (باستثناء الجمعة والسبت)</span>
                </div>
                <div className="flex items-start gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>سيتم تطبيق الراتب النسبي حسب ساعات العمل المسجلة في التقفيل اليومي</span>
                </div>
                <div className="flex items-start gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <span>تغيير الإعدادات سيؤثر على جميع حسابات الرواتب المستقبلية</span>
                </div>
                <div className="flex items-start gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <span>سيتم إلغاء تفعيل الإعدادات السابقة عند حفظ إعدادات جديدة</span>
                </div>
              </div>
            </div>

            {/* Current Settings Info */}
            {currentSettings && (
              <Card className="bg-muted/50">
                <CardHeader>
                  <CardTitle className="text-sm">الإعدادات الحالية</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>الراتب الأساسي:</span>
                    <span className="font-medium">{formatCurrency(currentSettings.base_salary)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>ساعات العمل اليومية:</span>
                    <span className="font-medium">{currentSettings.working_hours_per_day} ساعة</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>تاريخ السريان:</span>
                    <span className="font-medium">{new Date(currentSettings.effective_from).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>الحالة:</span>
                    <Badge variant={currentSettings.is_active ? "default" : "secondary"}>
                      {currentSettings.is_active ? "نشط" : "غير نشط"}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Submit Button */}
            <div className="flex justify-center">
              <Button 
                type="submit" 
                disabled={loading}
                className="gap-2"
                size="lg"
              >
                <Save className="h-4 w-4" />
                {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
