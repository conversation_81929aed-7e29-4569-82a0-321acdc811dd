'use client'

import { useEffect, useRef, useCallback } from 'react'
import { supabase } from '@/lib/supabase'

export function useSupabaseConnection() {
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isReconnectingRef = useRef(false)
  const lastVisibilityChangeRef = useRef<number>(0)

  const handleVisibilityChange = useCallback(() => {
    const now = Date.now()

    // Throttle visibility changes to prevent excessive re-renders
    if (now - lastVisibilityChangeRef.current < 1000) { // 1 second throttle
      return
    }

    lastVisibilityChangeRef.current = now

    if (document.visibilityState === 'visible' && !isReconnectingRef.current) {
      // <PERSON>rowser regained focus, refresh the connection
      isReconnectingRef.current = true

      // Clear any existing timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }

      // Small delay to ensure the browser is fully active
      reconnectTimeoutRef.current = setTimeout(() => {
        try {
          // Only refresh session if it's been more than 30 seconds since last visibility change
          if (now - lastVisibilityChangeRef.current >= 30000) {
            // Force a fresh connection by getting session
            supabase.auth.getSession().then(() => {
              console.log('Supabase connection refreshed after focus')
            }).catch((error) => {
              console.warn('Failed to refresh Supabase connection:', error)
            }).finally(() => {
              isReconnectingRef.current = false
            })
          } else {
            console.log('Skipping connection refresh - too recent')
            isReconnectingRef.current = false
          }
        } catch (error) {
          console.warn('Error refreshing Supabase connection:', error)
          isReconnectingRef.current = false
        }
      }, 500)
    }
  }, [])

  const handleOnline = useCallback(() => {
    // Network came back online
    if (!isReconnectingRef.current) {
      handleVisibilityChange()
    }
  }, [handleVisibilityChange])

  useEffect(() => {
    // Listen for visibility changes (tab focus/blur)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // Listen for network status changes
    window.addEventListener('online', handleOnline)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('online', handleOnline)
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, [handleVisibilityChange, handleOnline])

  // Return a function to manually trigger reconnection
  const forceReconnect = useCallback(() => {
    if (!isReconnectingRef.current) {
      handleVisibilityChange()
    }
  }, [handleVisibilityChange])

  return { forceReconnect }
}