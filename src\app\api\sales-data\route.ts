import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get authenticated user
    const { data: authData, error: authError } = await supabase.auth.getUser()
    
    if (authError || !authData.user) {
      return NextResponse.json({
        error: 'Authentication required'
      }, { status: 401 })
    }

    const userId = authData.user.id
    
    // Get user profile to determine role and permissions
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, role, area_id, team_id')
      .eq('id', userId)
      .single()
    
    if (profileError || !profile) {
      return NextResponse.json({
        error: 'Profile not found'
      }, { status: 404 })
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const selectedEmployee = searchParams.get('selectedEmployee')
    const selectedArea = searchParams.get('selectedArea')
    const selectedTeam = searchParams.get('selectedTeam')

    // Build base query
    let query = supabase
      .from('daily_closings')
      .select(`
        id,
        closing_date,
        user_id,
        total_sales_amount,
        cash_delivered,
        deficit_amount,
        advances_amount,
        price_breaks_amount,
        cash_confirmed,
        notes,
        attendance_records(check_in_time, check_out_time),
        profiles!daily_closings_user_id_fkey(
          id, full_name, email, role, area_id, team_id
        )
      `)
      .eq('sales_submitted', true)

    // Apply role-based filtering
    if (profile.role === 'sales_employee') {
      query = query.eq('user_id', userId)
    } else if (profile.role === 'team_manager' && profile.team_id) {
      // Get team members
      const { data: teamMembers } = await supabase
        .from('profiles')
        .select('id')
        .eq('team_id', profile.team_id)

      if (teamMembers && teamMembers.length > 0) {
        const memberIds = teamMembers.map(member => member.id)
        query = query.in('user_id', memberIds)
      }
    } else if (profile.role === 'area_manager' && profile.area_id) {
      // Get area members
      const { data: areaMembers } = await supabase
        .from('profiles')
        .select('id')
        .eq('area_id', profile.area_id)

      if (areaMembers && areaMembers.length > 0) {
        const memberIds = areaMembers.map(member => member.id)
        query = query.in('user_id', memberIds)
      }
    }
    // System admins can see all data (no additional filter)

    // Apply date range filter
    if (dateFrom) {
      query = query.gte('closing_date', dateFrom)
    }
    if (dateTo) {
      query = query.lte('closing_date', dateTo)
    }

    // Apply employee filter
    if (selectedEmployee && selectedEmployee !== 'all') {
      query = query.eq('user_id', selectedEmployee)
    }

    // Apply area filter (for system admins)
    if (selectedArea && selectedArea !== 'all' && profile.role === 'system_admin') {
      const { data: areaMembers } = await supabase
        .from('profiles')
        .select('id')
        .eq('area_id', selectedArea)

      if (areaMembers && areaMembers.length > 0) {
        const memberIds = areaMembers.map(member => member.id)
        query = query.in('user_id', memberIds)
      }
    }

    // Apply team filter (for system admins and area managers)
    if (selectedTeam && selectedTeam !== 'all' && ['system_admin', 'area_manager'].includes(profile.role)) {
      const { data: teamMembers } = await supabase
        .from('profiles')
        .select('id')
        .eq('team_id', selectedTeam)

      if (teamMembers && teamMembers.length > 0) {
        const memberIds = teamMembers.map(member => member.id)
        query = query.in('user_id', memberIds)
      }
    }

    const { data, error } = await query.order('closing_date', { ascending: false })

    if (error) {
      console.error('Database query error:', error)
      return NextResponse.json({
        error: 'Database query failed',
        details: error.message
      }, { status: 500 })
    }

    // Helper function to calculate working hours
    const calculateWorkingHours = (attendanceRecords: any[]) => {
      if (!attendanceRecords || attendanceRecords.length === 0) return 0

      const record = attendanceRecords[0]
      if (!record.check_in_time || !record.check_out_time) return 0

      const checkIn = new Date(`2000-01-01T${record.check_in_time}`)
      let checkOut = new Date(`2000-01-01T${record.check_out_time}`)

      // Handle next day checkout
      if (checkOut < checkIn) {
        checkOut.setDate(checkOut.getDate() + 1)
      }

      const diffMs = checkOut.getTime() - checkIn.getTime()
      const diffHours = diffMs / (1000 * 60 * 60)

      return Math.max(0, diffHours) // Ensure non-negative
    }

    // Get unique area and team IDs
    const areaIds = [...new Set(data?.map(item => item.profiles?.area_id).filter(Boolean))]
    const teamIds = [...new Set(data?.map(item => item.profiles?.team_id).filter(Boolean))]

    // Fetch areas and teams data
    let areasData = []
    let teamsData = []

    if (areaIds.length > 0) {
      const { data: areas } = await supabase
        .from('areas')
        .select('id, name')
        .in('id', areaIds)
      areasData = areas || []
    }

    if (teamIds.length > 0) {
      const { data: teams } = await supabase
        .from('teams')
        .select('id, name')
        .in('id', teamIds)
      teamsData = teams || []
    }

    // Create lookup maps
    const areasMap = new Map(areasData.map(area => [area.id, area]))
    const teamsMap = new Map(teamsData.map(team => [team.id, team]))

    // Transform data
    let transformedData = (data || []).map(item => {
      const attendanceRecord = item.attendance_records && item.attendance_records.length > 0 ? item.attendance_records[0] : null

      return {
        id: item.id,
        closing_date: item.closing_date,
        user_id: item.user_id,
        total_sales_amount: item.total_sales_amount || 0,
        cash_delivered: item.cash_delivered || 0,
        deficit_amount: item.deficit_amount || 0,
        advances_amount: item.advances_amount || 0,
        price_breaks_amount: item.price_breaks_amount || 0,
        cash_confirmed: item.cash_confirmed || false,
        notes: item.notes || '',
        check_in_time: attendanceRecord?.check_in_time || '',
        check_out_time: attendanceRecord?.check_out_time || '',
        working_hours: calculateWorkingHours(item.attendance_records || []),
        employee: {
          id: item.profiles?.id || '',
          full_name: item.profiles?.full_name || '',
          email: item.profiles?.email || '',
          role: item.profiles?.role || '',
          area_id: item.profiles?.area_id,
          team_id: item.profiles?.team_id
        },
        area: item.profiles?.area_id ? areasMap.get(item.profiles.area_id) || null : null,
        team: item.profiles?.team_id ? teamsMap.get(item.profiles.team_id) || null : null
      }
    })



    return NextResponse.json({
      success: true,
      data: transformedData,
      count: transformedData.length
    })
    
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
