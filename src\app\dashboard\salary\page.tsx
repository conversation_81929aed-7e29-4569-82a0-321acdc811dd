import { Metadata } from 'next'
import { SalaryManagementDashboard } from '@/components/dashboard/salary/SalaryManagementDashboard'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'إدارة الرواتب - سحابة المدينة',
  description: 'حساب وإدارة رواتب الموظفين والخصومات'
}

export default async function SalaryPage() {
  const supabase = createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/auth/login')
  }

  // Get user profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/auth/login')
  }

  // Check if user has access to salary management
  const allowedRoles = ['system_admin', 'area_manager', 'team_manager', 'sales_employee']
  if (!allowedRoles.includes(profile.role)) {
    redirect('/dashboard')
  }

  return (
    <div className="container mx-auto py-6" dir="rtl">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">الرئيسية</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>إدارة الرواتب</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <SalaryManagementDashboard
        userRole={profile.role}
        userId={profile.id}
        userAreaId={profile.area_id}
        userTeamId={profile.team_id}
      />
    </div>
  )
}
