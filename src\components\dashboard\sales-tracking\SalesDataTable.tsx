'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
  Eye,
  Calculator,
  TrendingUp,
  TrendingDown,
  Minus,
  User
} from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

interface SalesData {
  id: string
  closing_date: string
  user_id: string
  total_sales_amount: number
  cash_delivered: number
  deficit_amount: number
  advances_amount: number
  price_breaks_amount: number
  working_hours?: number
  employee: {
    id: string
    full_name: string
    email: string
    role: string
    area_id?: string
    team_id?: string
  }
  area?: {
    id: string
    name: string
  }
  team?: {
    id: string
    name: string
  }
}

interface SalesDataTableProps {
  data: SalesData[]
  loading: boolean
}

const ITEMS_PER_PAGE = 10

export function SalesDataTable({ data, loading }: SalesDataTableProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [sortField, setSortField] = useState<keyof SalesData>('closing_date')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  // Sort data
  const sortedData = [...data].sort((a, b) => {
    let aValue: any = a[sortField]
    let bValue: any = b[sortField]

    // Handle nested properties
    if (sortField === 'employee') {
      aValue = a.employee.full_name
      bValue = b.employee.full_name
    }

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  // Paginate data
  const totalPages = Math.ceil(sortedData.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const endIndex = startIndex + ITEMS_PER_PAGE
  const paginatedData = sortedData.slice(startIndex, endIndex)

  const handleSort = (field: keyof SalesData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const formatCurrency = (amount: number) => {
    const formatted = amount.toLocaleString('ar-SA', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    })
    return `${formatted} ر.س`
  }

  // تنسيق النسب المئوية
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy', { locale: ar })
  }

  const getPerformanceIndicator = (item: SalesData) => {
    const efficiency = item.working_hours ? item.total_sales_amount / item.working_hours : 0
    
    if (efficiency > 100) {
      return <TrendingUp className="h-4 w-4 text-green-600" />
    } else if (efficiency > 50) {
      return <Minus className="h-4 w-4 text-yellow-600" />
    } else {
      return <TrendingDown className="h-4 w-4 text-red-600" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'system_admin':
        return 'bg-purple-100 text-purple-800'
      case 'area_manager':
        return 'bg-blue-100 text-blue-800'
      case 'team_manager':
        return 'bg-green-100 text-green-800'
      case 'sales_employee':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'system_admin':
        return 'مدير النظام'
      case 'area_manager':
        return 'مدير منطقة'
      case 'team_manager':
        return 'مدير فريق'
      case 'sales_employee':
        return 'موظف مبيعات'
      default:
        return role
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>جدول بيانات المبيعات</CardTitle>
          <CardDescription>عرض تفصيلي لبيانات المبيعات اليومية</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">جاري تحميل البيانات...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>جدول بيانات المبيعات</CardTitle>
          <CardDescription>عرض تفصيلي لبيانات المبيعات اليومية</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-muted-foreground">لا توجد بيانات مبيعات للعرض</p>
              <p className="text-sm text-muted-foreground mt-2">
                جرب تغيير الفلاتر أو نطاق التاريخ
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>جدول بيانات المبيعات</CardTitle>
        <CardDescription>
          عرض {paginatedData.length} من أصل {data.length} سجل مبيعات
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div
          className="w-full force-scrollbar border rounded-lg"
          style={{
            maxWidth: '100%'
          }}
        >
          <Table className="rtl" style={{ minWidth: '1400px', width: '1400px', tableLayout: 'fixed' }}>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead
                  className="text-right cursor-pointer hover:bg-muted transition-colors font-semibold"
                  onClick={() => handleSort('closing_date')}
                >
                  <div className="flex items-center justify-between">
                    <span>التاريخ</span>
                    {sortField === 'closing_date' && (
                      <span className="mr-2">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  className="text-right cursor-pointer hover:bg-muted transition-colors font-semibold"
                  onClick={() => handleSort('employee')}
                >
                  <div className="flex items-center justify-between">
                    <span>الموظف</span>
                    {sortField === 'employee' && (
                      <span className="mr-2">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead className="text-center font-semibold">المنطقة</TableHead>
                <TableHead className="text-center font-semibold">الفريق</TableHead>
                <TableHead
                  className="text-center cursor-pointer hover:bg-muted transition-colors font-semibold"
                  onClick={() => handleSort('total_sales_amount')}
                >
                  <div className="flex items-center justify-center">
                    <span>إجمالي المبيعات</span>
                    {sortField === 'total_sales_amount' && (
                      <span className="mr-2">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead className="text-center font-semibold">المبلغ المسلم</TableHead>
                <TableHead className="text-center font-semibold">العجز</TableHead>
                <TableHead className="text-center font-semibold">السلف</TableHead>
                <TableHead className="text-center font-semibold">ساعات العمل</TableHead>
                <TableHead className="text-center font-semibold">الأداء</TableHead>
                <TableHead className="text-center font-semibold">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.map((item) => (
                <TableRow key={item.id} className="hover:bg-muted/30 transition-colors">
                  <TableCell className="font-medium text-right py-4">
                    <div className="text-sm">
                      {formatDate(item.closing_date)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right py-4">
                    <div className="flex items-center gap-3">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{item.employee.full_name}</p>
                        <p className="text-xs text-muted-foreground">
                          {item.employee.email}
                        </p>
                        <Badge
                          variant="secondary"
                          className={`text-xs mt-1 ${getRoleColor(item.employee.role)}`}
                        >
                          {getRoleLabel(item.employee.role)}
                        </Badge>
                      </div>
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  </TableCell>
                  <TableCell className="text-center py-4">
                    {item.area ? (
                      <Badge variant="outline" className="text-xs">
                        {item.area.name}
                      </Badge>
                    ) : (
                      <span className="text-xs text-muted-foreground">غير محدد</span>
                    )}
                  </TableCell>
                  <TableCell className="text-center py-4">
                    {item.team ? (
                      <Badge variant="outline" className="text-xs">
                        {item.team.name}
                      </Badge>
                    ) : (
                      <span className="text-xs text-muted-foreground">غير محدد</span>
                    )}
                  </TableCell>
                  <TableCell className="text-center font-medium py-4">
                    <div className="text-sm font-semibold text-green-600">
                      {formatCurrency(item.total_sales_amount)}
                    </div>
                  </TableCell>
                  <TableCell className="text-center py-4">
                    <div className="text-sm">
                      {formatCurrency(item.cash_delivered)}
                    </div>
                  </TableCell>
                  <TableCell className="text-center py-4">
                    {item.deficit_amount > 0 ? (
                      <Badge variant="destructive" className="text-xs">
                        {formatCurrency(item.deficit_amount)}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-center py-4">
                    {item.advances_amount > 0 ? (
                      <Badge variant="outline" className="text-xs">
                        {formatCurrency(item.advances_amount)}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {item.working_hours ? (
                      <Badge variant="secondary" className="text-xs">
                        {item.working_hours.toFixed(1)} ساعة
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {getPerformanceIndicator(item)}
                  </TableCell>
                  <TableCell className="text-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="gap-2">
                          <Eye className="h-4 w-4" />
                          عرض التفاصيل
                        </DropdownMenuItem>
                        <DropdownMenuItem className="gap-2">
                          <Calculator className="h-4 w-4" />
                          حساب الراتب
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-2 py-4">
            <div className="text-sm text-muted-foreground">
              عرض {startIndex + 1} إلى {Math.min(endIndex, data.length)} من أصل {data.length} سجل
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <div className="flex items-center gap-1">
                <span className="text-sm">صفحة</span>
                <span className="text-sm font-medium">{currentPage}</span>
                <span className="text-sm">من</span>
                <span className="text-sm font-medium">{totalPages}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
