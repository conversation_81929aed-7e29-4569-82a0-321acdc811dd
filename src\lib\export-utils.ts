import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

export interface ExportColumn {
  key: string
  header: string
  formatter?: (value: any) => string
}

export interface ExportOptions {
  filename?: string
  includeTimestamp?: boolean
  rtlSupport?: boolean
}

/**
 * Export data to CSV format with Arabic RTL support
 */
export function exportToCSV(
  data: any[],
  columns: ExportColumn[],
  options: ExportOptions = {}
) {
  const {
    filename = 'export',
    includeTimestamp = true,
    rtlSupport = true
  } = options

  // Add BOM for proper Arabic character encoding
  const BOM = rtlSupport ? '\uFEFF' : ''
  
  // Create headers
  const headers = columns.map(col => col.header)
  
  // Create data rows
  const rows = data.map(item => 
    columns.map(col => {
      const value = getNestedValue(item, col.key)
      return col.formatter ? col.formatter(value) : formatValue(value)
    })
  )

  // Combine headers and rows
  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    .join('\n')

  // Create final content with BOM
  const finalContent = BOM + csvContent

  // Generate filename
  const timestamp = includeTimestamp ? `_${format(new Date(), 'yyyy-MM-dd_HH-mm-ss')}` : ''
  const finalFilename = `${filename}${timestamp}.csv`

  // Download file
  downloadFile(finalContent, finalFilename, 'text/csv;charset=utf-8;')
}

/**
 * Export data to Excel-compatible CSV format
 */
export function exportToExcel(
  data: any[],
  columns: ExportColumn[],
  options: ExportOptions = {}
) {
  // Excel prefers tab-separated values for better Arabic support
  const {
    filename = 'export',
    includeTimestamp = true,
    rtlSupport = true
  } = options

  const BOM = rtlSupport ? '\uFEFF' : ''
  
  const headers = columns.map(col => col.header)
  
  const rows = data.map(item => 
    columns.map(col => {
      const value = getNestedValue(item, col.key)
      return col.formatter ? col.formatter(value) : formatValue(value)
    })
  )

  // Use tab separation for Excel compatibility
  const content = [headers, ...rows]
    .map(row => row.map(cell => String(cell).replace(/\t/g, ' ')).join('\t'))
    .join('\n')

  const finalContent = BOM + content
  const timestamp = includeTimestamp ? `_${format(new Date(), 'yyyy-MM-dd_HH-mm-ss')}` : ''
  const finalFilename = `${filename}${timestamp}.xls`

  downloadFile(finalContent, finalFilename, 'application/vnd.ms-excel;charset=utf-8;')
}

/**
 * Export salary data with specific formatting
 */
export function exportSalaryData(data: any[], options: ExportOptions = {}) {
  const columns: ExportColumn[] = [
    { key: 'employee.full_name', header: 'اسم الموظف' },
    { key: 'employee.email', header: 'البريد الإلكتروني' },
    { key: 'employee.role', header: 'المنصب', formatter: formatRole },
    { key: 'month', header: 'الشهر', formatter: formatMonth },
    { key: 'year', header: 'السنة' },
    { key: 'base_salary', header: 'الراتب الأساسي', formatter: formatCurrency },
    { key: 'expected_working_hours', header: 'ساعات العمل المتوقعة', formatter: formatHours },
    { key: 'actual_working_hours', header: 'ساعات العمل الفعلية', formatter: formatHours },
    { key: 'working_days_in_month', header: 'أيام العمل في الشهر' },
    { key: 'actual_working_days', header: 'أيام العمل الفعلية' },
    { key: 'proportional_salary', header: 'الراتب النسبي', formatter: formatCurrency },
    { key: 'deficit_deductions', header: 'خصومات العجز', formatter: formatCurrency },
    { key: 'advance_deductions', header: 'خصومات السلف', formatter: formatCurrency },
    { key: 'total_deductions', header: 'إجمالي الخصومات', formatter: formatCurrency },
    { key: 'final_salary', header: 'صافي الراتب', formatter: formatCurrency },
    { key: 'efficiency', header: 'كفاءة العمل', formatter: formatPercentage },
    { key: 'calculation_date', header: 'تاريخ الحساب', formatter: formatDate }
  ]

  return { columns, data }
}

/**
 * Export sales data with specific formatting
 */
export function exportSalesData(data: any[], options: ExportOptions = {}) {
  const columns: ExportColumn[] = [
    { key: 'date', header: 'التاريخ', formatter: formatDate },
    { key: 'employee.full_name', header: 'اسم الموظف' },
    { key: 'employee.email', header: 'البريد الإلكتروني' },
    { key: 'employee.area.name', header: 'المنطقة' },
    { key: 'employee.team.name', header: 'الفريق' },
    { key: 'employee.role', header: 'المنصب', formatter: formatRole },
    { key: 'sales_amount', header: 'مبلغ المبيعات', formatter: formatCurrency },
    { key: 'working_hours', header: 'ساعات العمل', formatter: formatHours },
    { key: 'deficit_amount', header: 'مبلغ العجز', formatter: formatCurrency },
    { key: 'efficiency', header: 'الكفاءة', formatter: formatPercentage },
    { key: 'performance_rating', header: 'تقييم الأداء', formatter: formatPerformance }
  ]

  return { columns, data }
}

/**
 * Export employee advances data
 */
export function exportAdvancesData(data: any[], options: ExportOptions = {}) {
  const columns: ExportColumn[] = [
    { key: 'employee.full_name', header: 'اسم الموظف' },
    { key: 'employee.email', header: 'البريد الإلكتروني' },
    { key: 'amount', header: 'مبلغ السلفة', formatter: formatCurrency },
    { key: 'reason', header: 'السبب' },
    { key: 'status', header: 'الحالة', formatter: formatAdvanceStatus },
    { key: 'request_date', header: 'تاريخ الطلب', formatter: formatDate },
    { key: 'approved_date', header: 'تاريخ الموافقة', formatter: formatDate },
    { key: 'deducted_date', header: 'تاريخ الخصم', formatter: formatDate }
  ]

  return { columns, data }
}

// Helper functions
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

function formatValue(value: any): string {
  if (value === null || value === undefined) return ''
  if (typeof value === 'boolean') return value ? 'نعم' : 'لا'
  return String(value)
}

function formatCurrency(amount: number | null | undefined): string {
  if (amount === null || amount === undefined) return '0.00 ر.س'
  return `${amount.toLocaleString('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ر.س`
}

function formatHours(hours: number | null | undefined): string {
  if (hours === null || hours === undefined) return '0 ساعة'
  return `${hours.toFixed(1)} ساعة`
}

function formatPercentage(value: number | null | undefined): string {
  if (value === null || value === undefined) return '0%'
  return `${value.toFixed(1)}%`
}

function formatDate(date: string | null | undefined): string {
  if (!date) return ''
  try {
    return format(new Date(date), 'dd/MM/yyyy', { locale: ar })
  } catch {
    return date
  }
}

function formatMonth(month: number | null | undefined): string {
  if (!month) return ''
  const months = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ]
  return months[month - 1] || ''
}

function formatRole(role: string | null | undefined): string {
  if (!role) return ''
  const roleMap: Record<string, string> = {
    'system_admin': 'مدير النظام',
    'area_manager': 'مدير منطقة',
    'team_manager': 'مدير فريق',
    'sales_employee': 'موظف مبيعات'
  }
  return roleMap[role] || role
}

function formatAdvanceStatus(status: string | null | undefined): string {
  if (!status) return ''
  const statusMap: Record<string, string> = {
    'pending': 'في الانتظار',
    'approved': 'موافق عليها',
    'deducted': 'تم الخصم',
    'cancelled': 'ملغية'
  }
  return statusMap[status] || status
}

function formatPerformance(rating: string | null | undefined): string {
  if (!rating) return ''
  const ratingMap: Record<string, string> = {
    'excellent': 'ممتاز',
    'good': 'جيد',
    'average': 'متوسط',
    'poor': 'ضعيف'
  }
  return ratingMap[rating] || rating
}

function downloadFile(content: string, filename: string, mimeType: string) {
  const blob = new Blob([content], { type: mimeType })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}
